// Sales Management System for Jerzouna Market POS
// Author: Oussema Souli

/**
 * نظام إدارة المبيعات
 */
class SalesManager {
    constructor() {
        this.currentInvoice = {
            items: [],
            subtotal: 0,
            tax: 0,
            total: 0,
            customerId: null,
            customerName: ''
        };
        this.taxRate = 19; // معدل الضريبة 19%
        this.initializeSalesSystem();
    }

    /**
     * تهيئة نظام المبيعات
     */
    initializeSalesSystem() {
        this.setupEventListeners();
        this.loadCustomersToSelect();
        this.loadProductsToSelect();
        this.generateNewInvoiceNumber();
        this.loadSalesHistory();
    }

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners() {
        // أزرار المبيعات
        const newSaleBtn = document.getElementById('newSaleBtn');
        const addProductBtn = document.getElementById('addProductBtn');
        const salesForm = document.getElementById('salesForm');
        const printInvoiceBtn = document.getElementById('printInvoiceBtn');
        const cancelSaleBtn = document.getElementById('cancelSaleBtn');

        if (newSaleBtn) {
            newSaleBtn.addEventListener('click', () => this.startNewSale());
        }

        if (addProductBtn) {
            addProductBtn.addEventListener('click', () => this.addProductToInvoice());
        }

        if (salesForm) {
            salesForm.addEventListener('submit', (e) => this.saveSale(e));
        }

        if (printInvoiceBtn) {
            printInvoiceBtn.addEventListener('click', () => this.printCurrentInvoice());
        }

        if (cancelSaleBtn) {
            cancelSaleBtn.addEventListener('click', () => this.cancelCurrentSale());
        }

        // تحديث السعر عند تغيير المنتج
        const productSelect = document.getElementById('productSelect');
        if (productSelect) {
            productSelect.addEventListener('change', () => this.updateProductPrice());
        }

        // تحديث المجموع عند تغيير الكمية أو السعر
        const quantityInput = document.getElementById('quantity');
        const unitPriceInput = document.getElementById('unitPrice');
        
        if (quantityInput) {
            quantityInput.addEventListener('input', () => this.updateProductTotal());
        }
        
        if (unitPriceInput) {
            unitPriceInput.addEventListener('input', () => this.updateProductTotal());
        }

        // فلتر سجل المبيعات
        const salesFilter = document.getElementById('salesFilter');
        if (salesFilter) {
            salesFilter.addEventListener('change', () => this.filterSalesHistory());
        }
    }

    /**
     * تحميل العملاء إلى القائمة المنسدلة
     */
    loadCustomersToSelect() {
        const customerSelect = document.getElementById('customerSelect');
        if (!customerSelect) return;

        const customers = database.getCustomers();
        customerSelect.innerHTML = '<option value="">اختر العميل</option>';
        
        customers.forEach(customer => {
            const option = document.createElement('option');
            option.value = customer.id;
            option.textContent = customer.name;
            customerSelect.appendChild(option);
        });
    }

    /**
     * تحميل المنتجات إلى القائمة المنسدلة
     */
    loadProductsToSelect() {
        const productSelect = document.getElementById('productSelect');
        if (!productSelect) return;

        const products = database.getProducts();
        productSelect.innerHTML = '<option value="">اختر المنتج</option>';
        
        products.forEach(product => {
            if (product.currentStock > 0) {
                const option = document.createElement('option');
                option.value = product.id;
                option.textContent = `${product.name} (${product.currentStock} ${product.unit})`;
                option.dataset.price = product.salePrice;
                option.dataset.stock = product.currentStock;
                productSelect.appendChild(option);
            }
        });
    }

    /**
     * توليد رقم فاتورة جديد
     */
    generateNewInvoiceNumber() {
        const invoiceNumberInput = document.getElementById('invoiceNumber');
        if (invoiceNumberInput) {
            invoiceNumberInput.value = database.generateInvoiceNumber();
        }
    }

    /**
     * بدء مبيعة جديدة
     */
    startNewSale() {
        this.resetCurrentInvoice();
        this.generateNewInvoiceNumber();
        this.loadCustomersToSelect();
        this.loadProductsToSelect();
        this.updateInvoiceDisplay();
        
        // التركيز على اختيار العميل
        const customerSelect = document.getElementById('customerSelect');
        if (customerSelect) {
            customerSelect.focus();
        }

        NotificationUtils.showInfo('تم بدء فاتورة جديدة');
    }

    /**
     * إعادة تعيين الفاتورة الحالية
     */
    resetCurrentInvoice() {
        this.currentInvoice = {
            items: [],
            subtotal: 0,
            tax: 0,
            total: 0,
            customerId: null,
            customerName: ''
        };

        // مسح النموذج
        const salesForm = document.getElementById('salesForm');
        if (salesForm) {
            salesForm.reset();
        }

        // مسح جدول المنتجات
        const invoiceItems = document.getElementById('invoiceItems');
        if (invoiceItems) {
            invoiceItems.innerHTML = '';
        }
    }

    /**
     * تحديث سعر المنتج عند الاختيار
     */
    updateProductPrice() {
        const productSelect = document.getElementById('productSelect');
        const unitPriceInput = document.getElementById('unitPrice');
        
        if (!productSelect || !unitPriceInput) return;

        const selectedOption = productSelect.options[productSelect.selectedIndex];
        if (selectedOption && selectedOption.dataset.price) {
            unitPriceInput.value = selectedOption.dataset.price;
            this.updateProductTotal();
        }
    }

    /**
     * تحديث مجموع المنتج
     */
    updateProductTotal() {
        const quantity = NumberUtils.parseNumber(document.getElementById('quantity')?.value || 0);
        const unitPrice = NumberUtils.parseNumber(document.getElementById('unitPrice')?.value || 0);
        
        // يمكن إضافة عرض المجموع الجزئي هنا إذا لزم الأمر
    }

    /**
     * إضافة منتج إلى الفاتورة
     */
    addProductToInvoice() {
        const productSelect = document.getElementById('productSelect');
        const quantityInput = document.getElementById('quantity');
        const unitPriceInput = document.getElementById('unitPrice');

        if (!productSelect || !quantityInput || !unitPriceInput) return;

        const productId = productSelect.value;
        const quantity = NumberUtils.parseNumber(quantityInput.value);
        const unitPrice = NumberUtils.parseNumber(unitPriceInput.value);

        // التحقق من صحة البيانات
        if (!productId) {
            NotificationUtils.showError('يرجى اختيار منتج');
            return;
        }

        if (quantity <= 0) {
            NotificationUtils.showError('يرجى إدخال كمية صحيحة');
            return;
        }

        if (unitPrice <= 0) {
            NotificationUtils.showError('يرجى إدخال سعر صحيح');
            return;
        }

        // التحقق من توفر المخزون
        const product = database.getProduct(productId);
        if (!product) {
            NotificationUtils.showError('المنتج غير موجود');
            return;
        }

        if (quantity > product.currentStock) {
            NotificationUtils.showError(`الكمية المطلوبة غير متوفرة. المتوفر: ${product.currentStock} ${product.unit}`);
            return;
        }

        // التحقق من وجود المنتج في الفاتورة
        const existingItemIndex = this.currentInvoice.items.findIndex(item => item.productId === productId);
        
        if (existingItemIndex !== -1) {
            // تحديث الكمية للمنتج الموجود
            const existingItem = this.currentInvoice.items[existingItemIndex];
            const newQuantity = existingItem.quantity + quantity;
            
            if (newQuantity > product.currentStock) {
                NotificationUtils.showError(`إجمالي الكمية يتجاوز المتوفر. المتوفر: ${product.currentStock} ${product.unit}`);
                return;
            }
            
            existingItem.quantity = newQuantity;
            existingItem.total = existingItem.quantity * existingItem.unitPrice;
        } else {
            // إضافة منتج جديد
            const newItem = {
                productId: productId,
                productName: product.name,
                quantity: quantity,
                unitPrice: unitPrice,
                total: quantity * unitPrice,
                unit: product.unit
            };
            
            this.currentInvoice.items.push(newItem);
        }

        // تحديث العرض
        this.updateInvoiceDisplay();
        this.clearProductInputs();
        
        NotificationUtils.showSuccess(`تم إضافة ${product.name} إلى الفاتورة`);
    }

    /**
     * مسح حقول إدخال المنتج
     */
    clearProductInputs() {
        const productSelect = document.getElementById('productSelect');
        const quantityInput = document.getElementById('quantity');
        const unitPriceInput = document.getElementById('unitPrice');

        if (productSelect) productSelect.value = '';
        if (quantityInput) quantityInput.value = '';
        if (unitPriceInput) unitPriceInput.value = '';
    }

    /**
     * حذف منتج من الفاتورة
     */
    removeItemFromInvoice(index) {
        if (index >= 0 && index < this.currentInvoice.items.length) {
            const removedItem = this.currentInvoice.items[index];
            this.currentInvoice.items.splice(index, 1);
            this.updateInvoiceDisplay();
            NotificationUtils.showInfo(`تم حذف ${removedItem.productName} من الفاتورة`);
        }
    }

    /**
     * تحديث عرض الفاتورة
     */
    updateInvoiceDisplay() {
        this.updateInvoiceTable();
        this.calculateTotals();
        this.updateTotalsDisplay();
    }

    /**
     * تحديث جدول الفاتورة
     */
    updateInvoiceTable() {
        const invoiceItems = document.getElementById('invoiceItems');
        if (!invoiceItems) return;

        invoiceItems.innerHTML = '';

        this.currentInvoice.items.forEach((item, index) => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${item.productName}</td>
                <td>${item.quantity} ${item.unit}</td>
                <td>${NumberUtils.formatCurrency(item.unitPrice)}</td>
                <td>${NumberUtils.formatCurrency(item.total)}</td>
                <td>
                    <button type="button" class="btn btn-danger btn-sm" onclick="salesManager.removeItemFromInvoice(${index})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            invoiceItems.appendChild(row);
        });
    }

    /**
     * حساب المجاميع
     */
    calculateTotals() {
        this.currentInvoice.subtotal = this.currentInvoice.items.reduce((sum, item) => sum + item.total, 0);
        this.currentInvoice.tax = NumberUtils.calculateTax(this.currentInvoice.subtotal, this.taxRate);
        this.currentInvoice.total = this.currentInvoice.subtotal + this.currentInvoice.tax;
    }

    /**
     * تحديث عرض المجاميع
     */
    updateTotalsDisplay() {
        const subtotalElement = document.getElementById('subtotal');
        const taxElement = document.getElementById('tax');
        const finalTotalElement = document.getElementById('finalTotal');

        if (subtotalElement) {
            subtotalElement.textContent = NumberUtils.formatCurrency(this.currentInvoice.subtotal);
        }

        if (taxElement) {
            taxElement.textContent = NumberUtils.formatCurrency(this.currentInvoice.tax);
        }

        if (finalTotalElement) {
            finalTotalElement.textContent = NumberUtils.formatCurrency(this.currentInvoice.total);
        }
    }

    /**
     * حفظ المبيعة
     */
    saveSale(event) {
        event.preventDefault();

        // التحقق من وجود منتجات في الفاتورة
        if (this.currentInvoice.items.length === 0) {
            NotificationUtils.showError('يرجى إضافة منتجات إلى الفاتورة');
            return;
        }

        // الحصول على بيانات العميل
        const customerSelect = document.getElementById('customerSelect');
        const customerId = customerSelect?.value || null;
        let customerName = 'عميل نقدي';

        if (customerId) {
            const customer = database.getCustomer(customerId);
            if (customer) {
                customerName = customer.name;
                this.currentInvoice.customerId = customerId;
                this.currentInvoice.customerName = customerName;
            }
        }

        // إنشاء بيانات المبيعة
        const saleData = {
            customerId: this.currentInvoice.customerId,
            customerName: customerName,
            items: this.currentInvoice.items,
            subtotal: this.currentInvoice.subtotal,
            tax: this.currentInvoice.tax,
            total: this.currentInvoice.total,
            taxRate: this.taxRate
        };

        try {
            // حفظ المبيعة في قاعدة البيانات
            const savedSale = database.addSale(saleData);
            
            NotificationUtils.showSuccess(`تم حفظ الفاتورة رقم ${savedSale.number} بنجاح`);
            
            // تحديث العرض
            this.loadSalesHistory();
            this.updateDashboardStats();
            
            // طباعة الفاتورة إذا كان مفعلاً في الإعدادات
            const settings = database.getSettings();
            if (settings.printAfterSale) {
                this.printInvoice(savedSale);
            }
            
            // بدء فاتورة جديدة
            this.startNewSale();
            
        } catch (error) {
            console.error('خطأ في حفظ المبيعة:', error);
            NotificationUtils.showError('حدث خطأ أثناء حفظ الفاتورة');
        }
    }

    /**
     * طباعة الفاتورة الحالية
     */
    printCurrentInvoice() {
        if (this.currentInvoice.items.length === 0) {
            NotificationUtils.showError('لا توجد منتجات للطباعة');
            return;
        }

        // إنشاء فاتورة مؤقتة للطباعة
        const tempInvoice = {
            number: document.getElementById('invoiceNumber')?.value || 'مسودة',
            date: new Date(),
            customerName: this.currentInvoice.customerName || 'عميل نقدي',
            customerPhone: '',
            items: this.currentInvoice.items,
            subtotal: this.currentInvoice.subtotal,
            tax: this.currentInvoice.tax,
            total: this.currentInvoice.total
        };

        this.printInvoice(tempInvoice);
    }

    /**
     * طباعة فاتورة
     */
    printInvoice(invoice) {
        const printContent = PrintUtils.createPrintableInvoice(invoice);
        
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>فاتورة ${invoice.number}</title>
                <style>
                    body { font-family: 'Cairo', Arial, sans-serif; direction: rtl; margin: 0; padding: 20px; }
                    @media print { body { margin: 0; padding: 10px; } }
                </style>
            </head>
            <body>
                ${printContent}
            </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
        printWindow.close();
    }

    /**
     * إلغاء المبيعة الحالية
     */
    cancelCurrentSale() {
        if (this.currentInvoice.items.length > 0) {
            if (confirm('هل أنت متأكد من إلغاء الفاتورة الحالية؟')) {
                this.startNewSale();
                NotificationUtils.showInfo('تم إلغاء الفاتورة');
            }
        }
    }

    /**
     * تحميل سجل المبيعات
     */
    loadSalesHistory() {
        const salesHistoryBody = document.getElementById('salesHistoryBody');
        if (!salesHistoryBody) return;

        const filter = document.getElementById('salesFilter')?.value || 'today';
        let sales = [];

        switch (filter) {
            case 'today':
                sales = database.getTodaySales();
                break;
            case 'yesterday':
                sales = database.getYesterdaySales();
                break;
            case 'week':
                const weekStart = new Date();
                weekStart.setDate(weekStart.getDate() - 7);
                sales = database.getSalesByDateRange(weekStart, new Date());
                break;
            case 'month':
                const monthStart = new Date();
                monthStart.setDate(1);
                sales = database.getSalesByDateRange(monthStart, new Date());
                break;
            default:
                sales = database.getSales();
        }

        salesHistoryBody.innerHTML = '';

        if (sales.length === 0) {
            salesHistoryBody.innerHTML = `
                <tr>
                    <td colspan="5" style="text-align: center; color: #7f8c8d; padding: 2rem;">
                        لا توجد مبيعات في الفترة المحددة
                    </td>
                </tr>
            `;
            return;
        }

        // ترتيب المبيعات حسب التاريخ (الأحدث أولاً)
        sales.sort((a, b) => new Date(b.date) - new Date(a.date));

        sales.forEach(sale => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${sale.number}</td>
                <td>${sale.customerName}</td>
                <td>${DateUtils.formatDateTime(new Date(sale.date))}</td>
                <td>${NumberUtils.formatCurrency(sale.total)}</td>
                <td>
                    <button class="btn btn-sm btn-secondary" onclick="salesManager.viewSaleDetails('${sale.id}')">
                        <i class="fas fa-eye"></i> عرض
                    </button>
                    <button class="btn btn-sm btn-primary" onclick="salesManager.printSale('${sale.id}')">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                </td>
            `;
            salesHistoryBody.appendChild(row);
        });
    }

    /**
     * تصفية سجل المبيعات
     */
    filterSalesHistory() {
        this.loadSalesHistory();
    }

    /**
     * عرض تفاصيل مبيعة
     */
    viewSaleDetails(saleId) {
        const sale = database.getSale(saleId);
        if (!sale) {
            NotificationUtils.showError('المبيعة غير موجودة');
            return;
        }

        // يمكن إضافة نافذة منبثقة لعرض التفاصيل
        alert(`تفاصيل الفاتورة ${sale.number}\n\nالعميل: ${sale.customerName}\nالتاريخ: ${DateUtils.formatDateTime(new Date(sale.date))}\nالمجموع: ${NumberUtils.formatCurrency(sale.total)}\n\nالمنتجات:\n${sale.items.map(item => `- ${item.productName}: ${item.quantity} ${item.unit} × ${NumberUtils.formatCurrency(item.unitPrice)} = ${NumberUtils.formatCurrency(item.total)}`).join('\n')}`);
    }

    /**
     * طباعة مبيعة محددة
     */
    printSale(saleId) {
        const sale = database.getSale(saleId);
        if (!sale) {
            NotificationUtils.showError('المبيعة غير موجودة');
            return;
        }

        // الحصول على بيانات العميل
        let customerPhone = '';
        if (sale.customerId) {
            const customer = database.getCustomer(sale.customerId);
            if (customer) {
                customerPhone = customer.phone || '';
            }
        }

        const invoiceData = {
            ...sale,
            customerPhone: customerPhone
        };

        this.printInvoice(invoiceData);
    }

    /**
     * تحديث إحصائيات لوحة التحكم
     */
    updateDashboardStats() {
        // سيتم استدعاء هذه الوظيفة من ملف app.js
        if (window.app && window.app.updateDashboardStats) {
            window.app.updateDashboardStats();
        }
    }
}

// إنشاء مثيل من مدير المبيعات
const salesManager = new SalesManager();

// تصدير مدير المبيعات للاستخدام في ملفات أخرى
window.salesManager = salesManager;
