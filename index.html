<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سوق الجملة للخضر والغلال بجرزونة - نقطة بيع عدد 14 - بيه الغالي</title>
    <link rel="stylesheet" href="styles/main.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-store"></i>
                <h1>سوق الجملة للخضر والغلال بجرزونة</h1>
                <span class="subtitle">نقطة بيع عدد 14 - بيه الغالي</span>
            </div>
            <div class="header-info">
                <div class="date-time">
                    <span id="currentDate"></span>
                    <span id="currentTime"></span>
                </div>
                <div class="owner-info">
                    <i class="fas fa-user"></i>
                    <span>حقوق الملكية: oussema souli</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <nav class="sidebar-nav">
                <ul>
                    <li class="nav-item active" data-section="dashboard">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>لوحة التحكم</span>
                    </li>
                    <li class="nav-item" data-section="customers">
                        <i class="fas fa-users"></i>
                        <span>قائمة الحرفاء</span>
                    </li>
                    <li class="nav-item" data-section="products">
                        <i class="fas fa-apple-alt"></i>
                        <span>قائمة البضائع</span>
                    </li>
                    <li class="nav-item" data-section="suppliers">
                        <i class="fas fa-truck"></i>
                        <span>قائمة الموردين</span>
                    </li>
                    <li class="nav-item" data-section="boxes">
                        <i class="fas fa-boxes"></i>
                        <span>قائمة الصناديق</span>
                    </li>
                    <li class="nav-item" data-section="purchases">
                        <i class="fas fa-shopping-cart"></i>
                        <span>قائمة المشتريات</span>
                    </li>
                    <li class="nav-item" data-section="supplier-invoices">
                        <i class="fas fa-file-invoice"></i>
                        <span>فواتير الموردين</span>
                    </li>
                    <li class="nav-item" data-section="receipts">
                        <i class="fas fa-receipt"></i>
                        <span>الوصولات</span>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Dashboard Section -->
            <section id="dashboard" class="content-section active">
                <div class="section-header">
                    <h2><i class="fas fa-tachometer-alt"></i> لوحة التحكم</h2>
                    <div class="date-picker-container">
                        <label>التاريخ:</label>
                        <input type="date" id="dashboardDatePicker" class="date-picker">
                    </div>
                </div>

                <!-- Stats Cards -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalCustomers">0</h3>
                            <p>إجمالي الحرفاء</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-apple-alt"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalProducts">0</h3>
                            <p>أنواع البضائع</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-truck"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalSuppliers">0</h3>
                            <p>إجمالي الموردين</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="todayPurchases">0</h3>
                            <p>مشتريات اليوم</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-boxes"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalBoxes">0</h3>
                            <p>إجمالي الصناديق</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="todayRevenue">0.00</h3>
                            <p>إيرادات اليوم (دينار)</p>
                        </div>
                    </div>
                </div>

                <!-- Recent Activities -->
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3><i class="fas fa-clock"></i> آخر العمليات</h3>
                        </div>
                        <div class="card-content">
                            <div id="recentActivities" class="activities-list">
                                <!-- Recent activities will be populated here -->
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3><i class="fas fa-exclamation-triangle"></i> تنبيهات الديون والرهون</h3>
                        </div>
                        <div class="card-content">
                            <div id="debtAlerts" class="alerts-list">
                                <!-- Debt and mortgage alerts will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Customers Section -->
            <section id="customers" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-users"></i> قائمة الحرفاء</h2>
                    <div class="section-controls">
                        <div class="search-container">
                            <input type="text" id="customerSearch" placeholder="بحث بالاسم أو رقم التعريف..." class="search-input">
                            <i class="fas fa-search search-icon"></i>
                        </div>
                        <button class="btn btn-primary" id="addCustomerBtn">
                            <i class="fas fa-plus"></i> إضافة حريف جديد
                        </button>
                    </div>
                </div>

                <div class="customers-table-card">
                    <div class="card-content">
                        <table id="customersTable" class="data-table">
                            <thead>
                                <tr>
                                    <th>رقم التعريف</th>
                                    <th>اسم الحريف</th>
                                    <th>الهاتف</th>
                                    <th>العنوان</th>
                                    <th>الديون</th>
                                    <th>الصناديق</th>
                                    <th>الرهون</th>
                                    <th>العمليات</th>
                                </tr>
                            </thead>
                            <tbody id="customersBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- Products Section -->
            <section id="products" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-apple-alt"></i> قائمة البضائع</h2>
                    <div class="section-controls">
                        <div class="date-picker-container">
                            <label>التاريخ:</label>
                            <input type="date" id="productsDatePicker" class="date-picker">
                        </div>
                        <div class="search-container">
                            <input type="text" id="productSearch" placeholder="بحث بالاسم أو رقم البضاعة..." class="search-input">
                            <i class="fas fa-search search-icon"></i>
                        </div>
                        <button class="btn btn-primary" id="addProductBtn">
                            <i class="fas fa-plus"></i> إضافة بضاعة جديدة
                        </button>
                    </div>
                </div>

                <div class="products-table-card">
                    <div class="card-content">
                        <table id="productsTable" class="data-table">
                            <thead>
                                <tr>
                                    <th>رقم البضاعة</th>
                                    <th>نوع البضاعة</th>
                                    <th>اسم المورد</th>
                                    <th>عدد الصناديق</th>
                                    <th>الوزن الصافي (كغ)</th>
                                    <th>سعر الكيلو (دينار)</th>
                                    <th>التاريخ والوقت</th>
                                    <th>العمليات</th>
                                </tr>
                            </thead>
                            <tbody id="productsBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- Suppliers Section -->
            <section id="suppliers" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-truck"></i> قائمة الموردين</h2>
                    <div class="section-controls">
                        <div class="search-container">
                            <input type="text" id="supplierSearch" placeholder="بحث بالاسم أو رقم المورد..." class="search-input">
                            <i class="fas fa-search search-icon"></i>
                        </div>
                        <button class="btn btn-primary" id="addSupplierBtn">
                            <i class="fas fa-plus"></i> إضافة مورد جديد
                        </button>
                    </div>
                </div>

                <div class="suppliers-table-card">
                    <div class="card-content">
                        <table id="suppliersTable" class="data-table">
                            <thead>
                                <tr>
                                    <th>رقم التعريف</th>
                                    <th>اسم المورد</th>
                                    <th>الهاتف</th>
                                    <th>العنوان</th>
                                    <th>عدد الصناديق المباعة</th>
                                    <th>نوع البضاعة</th>
                                    <th>الوزن الصافي (كغ)</th>
                                    <th>سعر البيع (دينار)</th>
                                    <th>العمليات</th>
                                </tr>
                            </thead>
                            <tbody id="suppliersBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- Boxes Section -->
            <section id="boxes" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-boxes"></i> قائمة الصناديق</h2>
                    <div class="section-controls">
                        <button class="btn btn-primary" id="addBoxTypeBtn">
                            <i class="fas fa-plus"></i> إضافة نوع صندوق جديد
                        </button>
                    </div>
                </div>

                <div class="boxes-table-card">
                    <div class="card-content">
                        <table id="boxesTable" class="data-table">
                            <thead>
                                <tr>
                                    <th>نوع الصندوق</th>
                                    <th>الوزن الفارغ (كغ)</th>
                                    <th>سعر الحمولة (دينار)</th>
                                    <th>الرهن (دينار)</th>
                                    <th>ملاحظات</th>
                                    <th>العمليات</th>
                                </tr>
                            </thead>
                            <tbody id="boxesBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- Purchases Section -->
            <section id="purchases" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-shopping-cart"></i> قائمة المشتريات</h2>
                    <div class="section-controls">
                        <div class="date-picker-container">
                            <label>التاريخ:</label>
                            <input type="date" id="purchasesDatePicker" class="date-picker">
                        </div>
                        <button class="btn btn-primary" id="newPurchaseBtn">
                            <i class="fas fa-plus"></i> عملية شراء جديدة
                        </button>
                    </div>
                </div>

                <!-- Purchase Form -->
                <div class="purchase-form-card" id="purchaseFormCard" style="display: none;">
                    <div class="card-header">
                        <h3>تسجيل عملية شراء جديدة</h3>
                        <button class="btn btn-secondary btn-sm" id="closePurchaseFormBtn">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="card-content">
                        <form id="purchaseForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <label>اسم الحريف *</label>
                                    <div class="autocomplete-container">
                                        <input type="text" id="customerNameInput" placeholder="ابحث عن الحريف..." required>
                                        <div id="customerSuggestions" class="suggestions-dropdown"></div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label>التاريخ والوقت</label>
                                    <input type="text" id="purchaseDateTime" readonly>
                                </div>
                            </div>

                            <div class="purchase-items-section">
                                <h4>تفاصيل المشتريات</h4>
                                <div class="purchase-item-input">
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label>البضاعة</label>
                                            <div class="autocomplete-container">
                                                <input type="text" id="productNameInput" placeholder="نوع البضاعة...">
                                                <div id="productSuggestions" class="suggestions-dropdown"></div>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label>المورد</label>
                                            <div class="autocomplete-container">
                                                <input type="text" id="supplierNameInput" placeholder="اسم المورد...">
                                                <div id="supplierSuggestions" class="suggestions-dropdown"></div>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label>سعر الكيلو (دينار)</label>
                                            <input type="number" id="pricePerKg" placeholder="0.00" min="0" step="0.01">
                                        </div>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label>عدد الصناديق</label>
                                            <input type="number" id="boxCount" placeholder="0" min="1" step="1">
                                        </div>
                                        <div class="form-group">
                                            <label>نوع الصندوق</label>
                                            <select id="boxType">
                                                <option value="">اختر نوع الصندوق</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label>الوزن القائم (كغ)</label>
                                            <input type="number" id="grossWeight" placeholder="0.00" min="0" step="0.01">
                                        </div>
                                        <div class="form-group">
                                            <label>الوزن الصافي (كغ)</label>
                                            <input type="number" id="netWeight" placeholder="0.00" readonly>
                                        </div>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label>المبلغ الجملي (دينار)</label>
                                            <input type="number" id="totalAmount" placeholder="0.00" readonly>
                                        </div>
                                        <div class="form-group checkbox-group">
                                            <label>
                                                <input type="checkbox" id="calculateMortgage"> حساب الرهن تلقائياً
                                            </label>
                                        </div>
                                        <div class="form-group checkbox-group">
                                            <label>
                                                <input type="checkbox" id="partialPayment"> دفع جزئي
                                            </label>
                                        </div>
                                    </div>

                                    <div class="payment-section" id="paymentSection" style="display: none;">
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label>المبلغ المدفوع (دينار)</label>
                                                <input type="number" id="paidAmount" placeholder="0.00" min="0" step="0.01">
                                            </div>
                                            <div class="form-group">
                                                <label>المبلغ المتبقي (دينار)</label>
                                                <input type="number" id="remainingAmount" placeholder="0.00" readonly>
                                            </div>
                                        </div>
                                    </div>

                                    <button type="button" id="addPurchaseItemBtn" class="btn btn-success">
                                        <i class="fas fa-plus"></i> إضافة إلى القائمة
                                    </button>
                                </div>

                                <div class="purchase-items-table">
                                    <table id="purchaseItemsTable">
                                        <thead>
                                            <tr>
                                                <th>البضاعة</th>
                                                <th>المورد</th>
                                                <th>سعر الكيلو</th>
                                                <th>عدد الصناديق</th>
                                                <th>نوع الصندوق</th>
                                                <th>الوزن القائم</th>
                                                <th>الوزن الصافي</th>
                                                <th>المبلغ الجملي</th>
                                                <th>العمليات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="purchaseItemsBody">
                                        </tbody>
                                    </table>
                                </div>

                                <div class="purchase-summary">
                                    <div class="summary-row">
                                        <span>إجمالي المبلغ:</span>
                                        <span id="purchaseTotal">0.00 دينار</span>
                                    </div>
                                    <div class="summary-row">
                                        <span>إجمالي الرهن:</span>
                                        <span id="mortgageTotal">0.00 دينار</span>
                                    </div>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> حفظ العملية
                                </button>
                                <button type="button" class="btn btn-secondary" id="printReceiptBtn">
                                    <i class="fas fa-print"></i> طباعة الوصل
                                </button>
                                <button type="button" class="btn btn-danger" id="cancelPurchaseBtn">
                                    <i class="fas fa-times"></i> إلغاء
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Purchases History -->
                <div class="purchases-table-card">
                    <div class="card-header">
                        <h3>سجل المشتريات</h3>
                        <div class="filter-controls">
                            <select id="purchasesFilter">
                                <option value="today">اليوم</option>
                                <option value="yesterday">الأمس</option>
                                <option value="week">هذا الأسبوع</option>
                                <option value="month">هذا الشهر</option>
                            </select>
                        </div>
                    </div>
                    <div class="card-content">
                        <table id="purchasesTable" class="data-table">
                            <thead>
                                <tr>
                                    <th>رقم العملية</th>
                                    <th>الحريف</th>
                                    <th>التاريخ والوقت</th>
                                    <th>عدد الأصناف</th>
                                    <th>المبلغ الإجمالي</th>
                                    <th>الرهن</th>
                                    <th>الحالة</th>
                                    <th>العمليات</th>
                                </tr>
                            </thead>
                            <tbody id="purchasesBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>
            <!-- Supplier Invoices Section -->
            <section id="supplier-invoices" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-file-invoice"></i> فواتير الموردين</h2>
                    <div class="section-controls">
                        <div class="date-picker-container">
                            <label>التاريخ:</label>
                            <input type="date" id="invoicesDatePicker" class="date-picker">
                        </div>
                        <button class="btn btn-primary" id="generateInvoicesBtn">
                            <i class="fas fa-print"></i> استخراج الفواتير
                        </button>
                    </div>
                </div>

                <div class="invoices-list-card" id="invoicesListCard" style="display: none;">
                    <div class="card-header">
                        <h3>فواتير الموردين لليوم المحدد</h3>
                    </div>
                    <div class="card-content">
                        <div id="supplierInvoicesList" class="suppliers-invoices-grid">
                            <!-- Supplier invoices will be populated here -->
                        </div>
                    </div>
                </div>
            </section>

            <!-- Receipts Section -->
            <section id="receipts" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-receipt"></i> الوصولات</h2>
                    <div class="section-controls">
                        <div class="date-picker-container">
                            <label>التاريخ:</label>
                            <input type="date" id="receiptsDatePicker" class="date-picker">
                        </div>
                        <div class="search-container">
                            <input type="text" id="receiptSearch" placeholder="بحث بالحريف أو رقم الوصل..." class="search-input">
                            <i class="fas fa-search search-icon"></i>
                        </div>
                    </div>
                </div>

                <div class="receipts-table-card">
                    <div class="card-content">
                        <table id="receiptsTable" class="data-table">
                            <thead>
                                <tr>
                                    <th>رقم الوصل</th>
                                    <th>اسم الحريف</th>
                                    <th>البضاعة والوزن</th>
                                    <th>المبلغ</th>
                                    <th>الرهن</th>
                                    <th>المدفوع</th>
                                    <th>المتبقي</th>
                                    <th>التاريخ والوقت</th>
                                    <th>العمليات</th>
                                </tr>
                            </thead>
                            <tbody id="receiptsBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Modals -->
    <div id="customerModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="customerModalTitle">إضافة حريف جديد</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <form id="customerForm">
                    <div class="form-group">
                        <label>رقم التعريف</label>
                        <input type="text" id="customerIdInput" placeholder="سيتم توليده تلقائياً" readonly>
                    </div>
                    <div class="form-group">
                        <label>اسم الحريف *</label>
                        <input type="text" id="customerNameModal" required>
                    </div>
                    <div class="form-group">
                        <label>رقم الهاتف</label>
                        <input type="tel" id="customerPhone">
                    </div>
                    <div class="form-group">
                        <label>العنوان</label>
                        <textarea id="customerAddress" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label>ملاحظات</label>
                        <textarea id="customerNotes" rows="2"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" id="saveCustomerBtn">حفظ</button>
                <button type="button" class="btn btn-secondary" id="cancelCustomerBtn">إلغاء</button>
            </div>
        </div>
    </div>

    <div id="productModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="productModalTitle">إضافة بضاعة جديدة</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <form id="productForm">
                    <div class="form-group">
                        <label>رقم البضاعة</label>
                        <input type="text" id="productIdInput" placeholder="سيتم توليده تلقائياً" readonly>
                    </div>
                    <div class="form-group">
                        <label>نوع البضاعة *</label>
                        <input type="text" id="productNameModal" required>
                    </div>
                    <div class="form-group">
                        <label>اسم المورد *</label>
                        <div class="autocomplete-container">
                            <input type="text" id="productSupplierModal" required>
                            <div id="productSupplierSuggestions" class="suggestions-dropdown"></div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>الفئة</label>
                        <select id="productCategory">
                            <option value="خضروات">خضروات</option>
                            <option value="فواكه">فواكه</option>
                            <option value="حبوب">حبوب</option>
                            <option value="بقوليات">بقوليات</option>
                            <option value="أخرى">أخرى</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>سعر الكيلو (دينار)</label>
                        <input type="number" id="productPrice" min="0" step="0.01">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" id="saveProductBtn">حفظ</button>
                <button type="button" class="btn btn-secondary" id="cancelProductBtn">إلغاء</button>
            </div>
        </div>
    </div>

    <div id="supplierModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="supplierModalTitle">إضافة مورد جديد</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <form id="supplierForm">
                    <div class="form-group">
                        <label>رقم التعريف</label>
                        <input type="text" id="supplierIdInput" placeholder="سيتم توليده تلقائياً" readonly>
                    </div>
                    <div class="form-group">
                        <label>اسم المورد *</label>
                        <input type="text" id="supplierNameModal" required>
                    </div>
                    <div class="form-group">
                        <label>رقم الهاتف</label>
                        <input type="tel" id="supplierPhone">
                    </div>
                    <div class="form-group">
                        <label>العنوان</label>
                        <textarea id="supplierAddress" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label>أنواع البضائع المتوفرة</label>
                        <textarea id="supplierProducts" rows="3" placeholder="مثال: طماطم، بطاطس، خيار..."></textarea>
                    </div>
                    <div class="form-group">
                        <label>ملاحظات</label>
                        <textarea id="supplierNotes" rows="2"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" id="saveSupplierBtn">حفظ</button>
                <button type="button" class="btn btn-secondary" id="cancelSupplierBtn">إلغاء</button>
            </div>
        </div>
    </div>

    <div id="boxModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="boxModalTitle">إضافة نوع صندوق جديد</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <form id="boxForm">
                    <div class="form-group">
                        <label>نوع الصندوق *</label>
                        <input type="text" id="boxTypeName" required>
                    </div>
                    <div class="form-group">
                        <label>الوزن الفارغ (كغ) *</label>
                        <input type="number" id="boxEmptyWeight" min="0" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label>سعر الحمولة (دينار) *</label>
                        <input type="number" id="boxLoadPrice" min="0" step="0.001" required>
                    </div>
                    <div class="form-group">
                        <label>الرهن (دينار)</label>
                        <input type="number" id="boxMortgage" min="0" step="0.01">
                    </div>
                    <div class="form-group">
                        <label>ملاحظات</label>
                        <textarea id="boxNotes" rows="2"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" id="saveBoxBtn">حفظ</button>
                <button type="button" class="btn btn-secondary" id="cancelBoxBtn">إلغاء</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/database.js"></script>
    <script src="js/customers.js"></script>
    <script src="js/products.js"></script>
    <script src="js/suppliers.js"></script>
    <script src="js/boxes.js"></script>
    <script src="js/purchases.js"></script>
    <script src="js/invoices.js"></script>
    <script src="js/receipts.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
