<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سوق الجملة للخضر والغلال بجرزونة - نقطة بيع عدد 14 - بيه الغالي</title>
    <link rel="stylesheet" href="styles/main.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-store"></i>
                <h1>سوق الجملة للخضر والغلال بجرزونة</h1>
                <span class="subtitle">نقطة بيع عدد 14 - بيه الغالي</span>
            </div>
            <div class="header-info">
                <div class="date-time">
                    <span id="currentDate"></span>
                    <span id="currentTime"></span>
                </div>
                <div class="owner-info">
                    <i class="fas fa-user"></i>
                    <span>المالك: oussema souli</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <nav class="sidebar-nav">
                <ul>
                    <li class="nav-item active" data-section="dashboard">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>لوحة التحكم</span>
                    </li>
                    <li class="nav-item" data-section="sales">
                        <i class="fas fa-cash-register"></i>
                        <span>المبيعات</span>
                    </li>
                    <li class="nav-item" data-section="inventory">
                        <i class="fas fa-boxes"></i>
                        <span>المخزون</span>
                    </li>
                    <li class="nav-item" data-section="customers">
                        <i class="fas fa-users"></i>
                        <span>العملاء</span>
                    </li>
                    <li class="nav-item" data-section="suppliers">
                        <i class="fas fa-truck"></i>
                        <span>الموردين</span>
                    </li>
                    <li class="nav-item" data-section="reports">
                        <i class="fas fa-chart-bar"></i>
                        <span>التقارير</span>
                    </li>
                    <li class="nav-item" data-section="settings">
                        <i class="fas fa-cog"></i>
                        <span>الإعدادات</span>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Dashboard Section -->
            <section id="dashboard" class="content-section active">
                <div class="section-header">
                    <h2><i class="fas fa-tachometer-alt"></i> لوحة التحكم</h2>
                </div>
                
                <!-- Stats Cards -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="todaySales">0.00</h3>
                            <p>مبيعات اليوم (دينار)</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="todayOrders">0</h3>
                            <p>عدد الطلبات اليوم</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-boxes"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalProducts">0</h3>
                            <p>إجمالي المنتجات</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalCustomers">0</h3>
                            <p>إجمالي العملاء</p>
                        </div>
                    </div>
                </div>

                <!-- Recent Activities -->
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3><i class="fas fa-clock"></i> آخر العمليات</h3>
                        </div>
                        <div class="card-content">
                            <div id="recentActivities" class="activities-list">
                                <!-- Recent activities will be populated here -->
                            </div>
                        </div>
                    </div>
                    
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3><i class="fas fa-exclamation-triangle"></i> تنبيهات المخزون</h3>
                        </div>
                        <div class="card-content">
                            <div id="stockAlerts" class="alerts-list">
                                <!-- Stock alerts will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Sales Section -->
            <section id="sales" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-cash-register"></i> نظام المبيعات</h2>
                    <button class="btn btn-primary" id="newSaleBtn">
                        <i class="fas fa-plus"></i> فاتورة جديدة
                    </button>
                </div>

                <div class="sales-container">
                    <!-- Sales Form -->
                    <div class="sales-form-card">
                        <div class="card-header">
                            <h3>إنشاء فاتورة جديدة</h3>
                        </div>
                        <div class="card-content">
                            <form id="salesForm">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>العميل</label>
                                        <select id="customerSelect" required>
                                            <option value="">اختر العميل</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>رقم الفاتورة</label>
                                        <input type="text" id="invoiceNumber" readonly>
                                    </div>
                                </div>
                                
                                <div class="products-section">
                                    <h4>المنتجات</h4>
                                    <div class="product-input">
                                        <select id="productSelect">
                                            <option value="">اختر المنتج</option>
                                        </select>
                                        <input type="number" id="quantity" placeholder="الكمية" min="0.1" step="0.1">
                                        <input type="number" id="unitPrice" placeholder="سعر الوحدة" min="0" step="0.01">
                                        <button type="button" id="addProductBtn" class="btn btn-success">
                                            <i class="fas fa-plus"></i> إضافة
                                        </button>
                                    </div>
                                    
                                    <div class="invoice-items">
                                        <table id="invoiceTable">
                                            <thead>
                                                <tr>
                                                    <th>المنتج</th>
                                                    <th>الكمية</th>
                                                    <th>سعر الوحدة</th>
                                                    <th>المجموع</th>
                                                    <th>العمليات</th>
                                                </tr>
                                            </thead>
                                            <tbody id="invoiceItems">
                                            </tbody>
                                        </table>
                                    </div>
                                    
                                    <div class="invoice-total">
                                        <div class="total-row">
                                            <span>المجموع الفرعي:</span>
                                            <span id="subtotal">0.00 دينار</span>
                                        </div>
                                        <div class="total-row">
                                            <span>الضريبة (19%):</span>
                                            <span id="tax">0.00 دينار</span>
                                        </div>
                                        <div class="total-row final-total">
                                            <span>المجموع النهائي:</span>
                                            <span id="finalTotal">0.00 دينار</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> حفظ الفاتورة
                                    </button>
                                    <button type="button" class="btn btn-secondary" id="printInvoiceBtn">
                                        <i class="fas fa-print"></i> طباعة
                                    </button>
                                    <button type="button" class="btn btn-danger" id="cancelSaleBtn">
                                        <i class="fas fa-times"></i> إلغاء
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Sales History -->
                <div class="sales-history-card">
                    <div class="card-header">
                        <h3>سجل المبيعات</h3>
                        <div class="filter-controls">
                            <select id="salesFilter">
                                <option value="today">اليوم</option>
                                <option value="yesterday">الأمس</option>
                                <option value="week">هذا الأسبوع</option>
                                <option value="month">هذا الشهر</option>
                            </select>
                        </div>
                    </div>
                    <div class="card-content">
                        <table id="salesHistoryTable" class="data-table">
                            <thead>
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>العميل</th>
                                    <th>التاريخ والوقت</th>
                                    <th>المجموع</th>
                                    <th>العمليات</th>
                                </tr>
                            </thead>
                            <tbody id="salesHistoryBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- Inventory Section -->
            <section id="inventory" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-boxes"></i> إدارة المخزون</h2>
                    <button class="btn btn-primary" id="addProductBtn">
                        <i class="fas fa-plus"></i> إضافة منتج
                    </button>
                </div>

                <!-- Inventory Grid -->
                <div class="inventory-grid">
                    <div id="inventoryCards" class="products-grid">
                        <!-- Products will be populated here -->
                    </div>
                </div>
            </section>

            <!-- Customers Section -->
            <section id="customers" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-users"></i> إدارة العملاء</h2>
                    <button class="btn btn-primary" id="addCustomerBtn">
                        <i class="fas fa-plus"></i> إضافة عميل
                    </button>
                </div>

                <div class="customers-table-card">
                    <div class="card-content">
                        <table id="customersTable" class="data-table">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>الهاتف</th>
                                    <th>العنوان</th>
                                    <th>إجمالي المشتريات</th>
                                    <th>العمليات</th>
                                </tr>
                            </thead>
                            <tbody id="customersBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- Suppliers Section -->
            <section id="suppliers" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-truck"></i> إدارة الموردين</h2>
                    <button class="btn btn-primary" id="addSupplierBtn">
                        <i class="fas fa-plus"></i> إضافة مورد
                    </button>
                </div>

                <div class="suppliers-table-card">
                    <div class="card-content">
                        <table id="suppliersTable" class="data-table">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>الهاتف</th>
                                    <th>العنوان</th>
                                    <th>المنتجات</th>
                                    <th>العمليات</th>
                                </tr>
                            </thead>
                            <tbody id="suppliersBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- Reports Section -->
            <section id="reports" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-chart-bar"></i> التقارير والإحصائيات</h2>
                </div>

                <div class="reports-grid">
                    <div class="report-card">
                        <div class="card-header">
                            <h3>تقرير المبيعات اليومية</h3>
                        </div>
                        <div class="card-content">
                            <div id="dailySalesChart" class="chart-container">
                                <!-- Chart will be rendered here -->
                            </div>
                        </div>
                    </div>

                    <div class="report-card">
                        <div class="card-header">
                            <h3>أفضل المنتجات مبيعاً</h3>
                        </div>
                        <div class="card-content">
                            <div id="topProductsChart" class="chart-container">
                                <!-- Chart will be rendered here -->
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Settings Section -->
            <section id="settings" class="content-section">
                <div class="section-header">
                    <h2><i class="fas fa-cog"></i> الإعدادات</h2>
                </div>

                <div class="settings-grid">
                    <div class="settings-card">
                        <div class="card-header">
                            <h3>إعدادات عامة</h3>
                        </div>
                        <div class="card-content">
                            <div class="form-group">
                                <label>اسم المحل</label>
                                <input type="text" id="storeName" value="سوق الجملة للخضر والغلال بجرزونة">
                            </div>
                            <div class="form-group">
                                <label>رقم نقطة البيع</label>
                                <input type="text" id="posNumber" value="14">
                            </div>
                            <div class="form-group">
                                <label>اسم المالك</label>
                                <input type="text" id="ownerName" value="بيه الغالي">
                            </div>
                        </div>
                    </div>

                    <div class="settings-card">
                        <div class="card-header">
                            <h3>النسخ الاحتياطي</h3>
                        </div>
                        <div class="card-content">
                            <button class="btn btn-primary" id="backupBtn">
                                <i class="fas fa-download"></i> تصدير البيانات
                            </button>
                            <button class="btn btn-secondary" id="restoreBtn">
                                <i class="fas fa-upload"></i> استيراد البيانات
                            </button>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Modals -->
    <div id="productModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="productModalTitle">إضافة منتج جديد</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <form id="productForm">
                    <div class="form-group">
                        <label>اسم المنتج</label>
                        <input type="text" id="productName" required>
                    </div>
                    <div class="form-group">
                        <label>الفئة</label>
                        <select id="productCategory" required>
                            <option value="">اختر الفئة</option>
                            <option value="خضروات">خضروات</option>
                            <option value="فواكه">فواكه</option>
                            <option value="حبوب">حبوب</option>
                            <option value="أخرى">أخرى</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>الوحدة</label>
                        <select id="productUnit" required>
                            <option value="">اختر الوحدة</option>
                            <option value="كيلو">كيلو</option>
                            <option value="قطعة">قطعة</option>
                            <option value="صندوق">صندوق</option>
                            <option value="كيس">كيس</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>سعر الشراء</label>
                        <input type="number" id="purchasePrice" min="0" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label>سعر البيع</label>
                        <input type="number" id="salePrice" min="0" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label>الكمية الحالية</label>
                        <input type="number" id="currentStock" min="0" step="0.1" required>
                    </div>
                    <div class="form-group">
                        <label>الحد الأدنى للمخزون</label>
                        <input type="number" id="minStock" min="0" step="0.1" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" id="saveProductBtn">حفظ</button>
                <button type="button" class="btn btn-secondary" id="cancelProductBtn">إلغاء</button>
            </div>
        </div>
    </div>

    <div id="customerModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="customerModalTitle">إضافة عميل جديد</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <form id="customerForm">
                    <div class="form-group">
                        <label>اسم العميل</label>
                        <input type="text" id="customerName" required>
                    </div>
                    <div class="form-group">
                        <label>رقم الهاتف</label>
                        <input type="tel" id="customerPhone">
                    </div>
                    <div class="form-group">
                        <label>العنوان</label>
                        <textarea id="customerAddress"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" id="saveCustomerBtn">حفظ</button>
                <button type="button" class="btn btn-secondary" id="cancelCustomerBtn">إلغاء</button>
            </div>
        </div>
    </div>

    <div id="supplierModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="supplierModalTitle">إضافة مورد جديد</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <form id="supplierForm">
                    <div class="form-group">
                        <label>اسم المورد</label>
                        <input type="text" id="supplierName" required>
                    </div>
                    <div class="form-group">
                        <label>رقم الهاتف</label>
                        <input type="tel" id="supplierPhone">
                    </div>
                    <div class="form-group">
                        <label>العنوان</label>
                        <textarea id="supplierAddress"></textarea>
                    </div>
                    <div class="form-group">
                        <label>المنتجات المتوفرة</label>
                        <textarea id="supplierProducts" placeholder="اكتب المنتجات مفصولة بفاصلة"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" id="saveSupplierBtn">حفظ</button>
                <button type="button" class="btn btn-secondary" id="cancelSupplierBtn">إلغاء</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/database.js"></script>
    <script src="js/sales.js"></script>
    <script src="js/inventory.js"></script>
    <script src="js/reports.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
