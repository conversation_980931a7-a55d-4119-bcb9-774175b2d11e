// Utility Functions for Jerzouna Market POS System
// Author: <PERSON><PERSON><PERSON> Souli

/**
 * تنسيق التاريخ والوقت
 */
const DateUtils = {
    // تنسيق التاريخ بالعربية
    formatDate: (date = new Date()) => {
        const options = {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            weekday: 'long'
        };
        return date.toLocaleDateString('ar-TN', options);
    },

    // تنسيق الوقت
    formatTime: (date = new Date()) => {
        const options = {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        };
        return date.toLocaleTimeString('ar-TN', options);
    },

    // تنسيق التاريخ والوقت معاً
    formatDateTime: (date = new Date()) => {
        return `${DateUtils.formatDate(date)} - ${DateUtils.formatTime(date)}`;
    },

    // التحقق من كون التاريخ اليوم
    isToday: (date) => {
        const today = new Date();
        const checkDate = new Date(date);
        return checkDate.toDateString() === today.toDateString();
    },

    // التحقق من كون التاريخ أمس
    isYesterday: (date) => {
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        const checkDate = new Date(date);
        return checkDate.toDateString() === yesterday.toDateString();
    },

    // الحصول على بداية اليوم
    getStartOfDay: (date = new Date()) => {
        const start = new Date(date);
        start.setHours(0, 0, 0, 0);
        return start;
    },

    // الحصول على نهاية اليوم
    getEndOfDay: (date = new Date()) => {
        const end = new Date(date);
        end.setHours(23, 59, 59, 999);
        return end;
    }
};

/**
 * تنسيق الأرقام والعملة
 */
const NumberUtils = {
    // تنسيق الأرقام بالفاصلة العشرية
    formatNumber: (number, decimals = 2) => {
        if (isNaN(number)) return '0.00';
        return parseFloat(number).toFixed(decimals);
    },

    // تنسيق العملة
    formatCurrency: (amount, currency = 'دينار') => {
        const formatted = NumberUtils.formatNumber(amount);
        return `${formatted} ${currency}`;
    },

    // تحويل النص إلى رقم
    parseNumber: (value) => {
        const number = parseFloat(value);
        return isNaN(number) ? 0 : number;
    },

    // حساب النسبة المئوية
    calculatePercentage: (value, total) => {
        if (total === 0) return 0;
        return (value / total) * 100;
    },

    // حساب الضريبة
    calculateTax: (amount, taxRate = 19) => {
        return (amount * taxRate) / 100;
    },

    // حساب المجموع مع الضريبة
    calculateTotalWithTax: (amount, taxRate = 19) => {
        const tax = NumberUtils.calculateTax(amount, taxRate);
        return amount + tax;
    }
};

/**
 * وظائف التحقق من صحة البيانات
 */
const ValidationUtils = {
    // التحقق من البريد الإلكتروني
    isValidEmail: (email) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },

    // التحقق من رقم الهاتف
    isValidPhone: (phone) => {
        const phoneRegex = /^[0-9+\-\s()]+$/;
        return phoneRegex.test(phone) && phone.length >= 8;
    },

    // التحقق من النص الفارغ
    isEmpty: (value) => {
        return !value || value.trim() === '';
    },

    // التحقق من الرقم الموجب
    isPositiveNumber: (value) => {
        const number = parseFloat(value);
        return !isNaN(number) && number > 0;
    },

    // التحقق من الرقم غير السالب
    isNonNegativeNumber: (value) => {
        const number = parseFloat(value);
        return !isNaN(number) && number >= 0;
    }
};

/**
 * وظائف التخزين المحلي
 */
const StorageUtils = {
    // حفظ البيانات
    save: (key, data) => {
        try {
            const jsonData = JSON.stringify(data);
            localStorage.setItem(key, jsonData);
            return true;
        } catch (error) {
            console.error('خطأ في حفظ البيانات:', error);
            return false;
        }
    },

    // استرجاع البيانات
    load: (key, defaultValue = null) => {
        try {
            const jsonData = localStorage.getItem(key);
            return jsonData ? JSON.parse(jsonData) : defaultValue;
        } catch (error) {
            console.error('خطأ في استرجاع البيانات:', error);
            return defaultValue;
        }
    },

    // حذف البيانات
    remove: (key) => {
        try {
            localStorage.removeItem(key);
            return true;
        } catch (error) {
            console.error('خطأ في حذف البيانات:', error);
            return false;
        }
    },

    // مسح جميع البيانات
    clear: () => {
        try {
            localStorage.clear();
            return true;
        } catch (error) {
            console.error('خطأ في مسح البيانات:', error);
            return false;
        }
    }
};

/**
 * وظائف DOM
 */
const DOMUtils = {
    // إنشاء عنصر HTML
    createElement: (tag, className = '', innerHTML = '') => {
        const element = document.createElement(tag);
        if (className) element.className = className;
        if (innerHTML) element.innerHTML = innerHTML;
        return element;
    },

    // إضافة مستمع للأحداث
    addEventListeners: (elements, event, handler) => {
        if (typeof elements === 'string') {
            elements = document.querySelectorAll(elements);
        }
        if (elements.length) {
            elements.forEach(element => {
                element.addEventListener(event, handler);
            });
        } else if (elements.addEventListener) {
            elements.addEventListener(event, handler);
        }
    },

    // إظهار/إخفاء العناصر
    show: (element) => {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        if (element) element.style.display = 'block';
    },

    hide: (element) => {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        if (element) element.style.display = 'none';
    },

    // تبديل الفئات
    toggleClass: (element, className) => {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        if (element) element.classList.toggle(className);
    },

    // مسح محتوى العنصر
    clearContent: (element) => {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        if (element) element.innerHTML = '';
    }
};

/**
 * وظائف الإشعارات
 */
const NotificationUtils = {
    // إظهار إشعار نجاح
    showSuccess: (message, duration = 3000) => {
        NotificationUtils.showNotification(message, 'success', duration);
    },

    // إظهار إشعار خطأ
    showError: (message, duration = 5000) => {
        NotificationUtils.showNotification(message, 'error', duration);
    },

    // إظهار إشعار تحذير
    showWarning: (message, duration = 4000) => {
        NotificationUtils.showNotification(message, 'warning', duration);
    },

    // إظهار إشعار معلومات
    showInfo: (message, duration = 3000) => {
        NotificationUtils.showNotification(message, 'info', duration);
    },

    // إظهار الإشعار
    showNotification: (message, type = 'info', duration = 3000) => {
        // إنشاء عنصر الإشعار
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${NotificationUtils.getIcon(type)}"></i>
                <span>${message}</span>
                <button class="notification-close">&times;</button>
            </div>
        `;

        // إضافة الأنماط إذا لم تكن موجودة
        if (!document.querySelector('#notification-styles')) {
            const styles = document.createElement('style');
            styles.id = 'notification-styles';
            styles.textContent = `
                .notification {
                    position: fixed;
                    top: 20px;
                    left: 20px;
                    min-width: 300px;
                    padding: 1rem;
                    border-radius: 8px;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
                    z-index: 10000;
                    animation: slideInLeft 0.3s ease;
                    font-family: 'Cairo', sans-serif;
                }
                .notification-success { background: #d4edda; color: #155724; border-left: 4px solid #28a745; }
                .notification-error { background: #f8d7da; color: #721c24; border-left: 4px solid #dc3545; }
                .notification-warning { background: #fff3cd; color: #856404; border-left: 4px solid #ffc107; }
                .notification-info { background: #d1ecf1; color: #0c5460; border-left: 4px solid #17a2b8; }
                .notification-content { display: flex; align-items: center; gap: 0.5rem; }
                .notification-close { background: none; border: none; font-size: 1.2rem; cursor: pointer; margin-right: auto; }
                @keyframes slideInLeft { from { transform: translateX(-100%); } to { transform: translateX(0); } }
            `;
            document.head.appendChild(styles);
        }

        // إضافة الإشعار إلى الصفحة
        document.body.appendChild(notification);

        // إضافة مستمع لزر الإغلاق
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            notification.remove();
        });

        // إزالة الإشعار تلقائياً
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, duration);
    },

    // الحصول على أيقونة الإشعار
    getIcon: (type) => {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }
};

/**
 * وظائف التصدير والاستيراد
 */
const ExportUtils = {
    // تصدير البيانات إلى JSON
    exportToJSON: (data, filename = 'data.json') => {
        const jsonData = JSON.stringify(data, null, 2);
        const blob = new Blob([jsonData], { type: 'application/json' });
        ExportUtils.downloadFile(blob, filename);
    },

    // تصدير البيانات إلى CSV
    exportToCSV: (data, filename = 'data.csv', headers = []) => {
        let csvContent = '';
        
        // إضافة العناوين
        if (headers.length > 0) {
            csvContent += headers.join(',') + '\n';
        }
        
        // إضافة البيانات
        data.forEach(row => {
            const values = Object.values(row).map(value => {
                // تنظيف القيم وإضافة علامات اقتباس إذا لزم الأمر
                const cleanValue = String(value).replace(/"/g, '""');
                return `"${cleanValue}"`;
            });
            csvContent += values.join(',') + '\n';
        });
        
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        ExportUtils.downloadFile(blob, filename);
    },

    // تحميل الملف
    downloadFile: (blob, filename) => {
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    },

    // استيراد ملف JSON
    importFromJSON: (file) => {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const data = JSON.parse(e.target.result);
                    resolve(data);
                } catch (error) {
                    reject(new Error('ملف JSON غير صالح'));
                }
            };
            reader.onerror = () => reject(new Error('خطأ في قراءة الملف'));
            reader.readAsText(file);
        });
    }
};

/**
 * وظائف البحث والتصفية
 */
const SearchUtils = {
    // البحث في النص
    searchText: (text, query) => {
        if (!query) return true;
        return text.toLowerCase().includes(query.toLowerCase());
    },

    // تصفية المصفوفة
    filterArray: (array, filters) => {
        return array.filter(item => {
            return Object.keys(filters).every(key => {
                const filterValue = filters[key];
                const itemValue = item[key];
                
                if (filterValue === '' || filterValue === null || filterValue === undefined) {
                    return true;
                }
                
                if (typeof filterValue === 'string') {
                    return SearchUtils.searchText(String(itemValue), filterValue);
                }
                
                return itemValue === filterValue;
            });
        });
    },

    // ترتيب المصفوفة
    sortArray: (array, key, direction = 'asc') => {
        return [...array].sort((a, b) => {
            const aValue = a[key];
            const bValue = b[key];
            
            if (aValue < bValue) return direction === 'asc' ? -1 : 1;
            if (aValue > bValue) return direction === 'asc' ? 1 : -1;
            return 0;
        });
    }
};

/**
 * وظائف الطباعة
 */
const PrintUtils = {
    // طباعة عنصر محدد
    printElement: (elementId) => {
        const element = document.getElementById(elementId);
        if (!element) {
            NotificationUtils.showError('العنصر المطلوب طباعته غير موجود');
            return;
        }
        
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>طباعة</title>
                <style>
                    body { font-family: 'Cairo', Arial, sans-serif; direction: rtl; }
                    @media print { body { margin: 0; } }
                </style>
            </head>
            <body>
                ${element.innerHTML}
            </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
        printWindow.close();
    },

    // إنشاء فاتورة للطباعة
    createPrintableInvoice: (invoice) => {
        return `
            <div style="max-width: 800px; margin: 0 auto; padding: 20px;">
                <div style="text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 20px;">
                    <h1>سوق الجملة للخضر والغلال بجرزونة</h1>
                    <h2>نقطة بيع عدد 14 - بيه الغالي</h2>
                    <p>المالك: oussema souli</p>
                </div>
                
                <div style="display: flex; justify-content: space-between; margin-bottom: 20px;">
                    <div>
                        <strong>رقم الفاتورة:</strong> ${invoice.number}<br>
                        <strong>التاريخ:</strong> ${DateUtils.formatDateTime(invoice.date)}
                    </div>
                    <div>
                        <strong>العميل:</strong> ${invoice.customerName}<br>
                        <strong>الهاتف:</strong> ${invoice.customerPhone || 'غير محدد'}
                    </div>
                </div>
                
                <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                    <thead>
                        <tr style="background: #f5f5f5;">
                            <th style="border: 1px solid #ddd; padding: 10px; text-align: right;">المنتج</th>
                            <th style="border: 1px solid #ddd; padding: 10px; text-align: center;">الكمية</th>
                            <th style="border: 1px solid #ddd; padding: 10px; text-align: center;">سعر الوحدة</th>
                            <th style="border: 1px solid #ddd; padding: 10px; text-align: center;">المجموع</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${invoice.items.map(item => `
                            <tr>
                                <td style="border: 1px solid #ddd; padding: 10px;">${item.productName}</td>
                                <td style="border: 1px solid #ddd; padding: 10px; text-align: center;">${item.quantity}</td>
                                <td style="border: 1px solid #ddd; padding: 10px; text-align: center;">${NumberUtils.formatCurrency(item.unitPrice)}</td>
                                <td style="border: 1px solid #ddd; padding: 10px; text-align: center;">${NumberUtils.formatCurrency(item.total)}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
                
                <div style="text-align: left; font-size: 18px;">
                    <div style="margin-bottom: 10px;">
                        <strong>المجموع الفرعي: ${NumberUtils.formatCurrency(invoice.subtotal)}</strong>
                    </div>
                    <div style="margin-bottom: 10px;">
                        <strong>الضريبة (19%): ${NumberUtils.formatCurrency(invoice.tax)}</strong>
                    </div>
                    <div style="font-size: 24px; border-top: 2px solid #333; padding-top: 10px;">
                        <strong>المجموع النهائي: ${NumberUtils.formatCurrency(invoice.total)}</strong>
                    </div>
                </div>
                
                <div style="text-align: center; margin-top: 40px; font-size: 14px; color: #666;">
                    <p>شكراً لتعاملكم معنا</p>
                    <p>تم إنشاء هذه الفاتورة بواسطة نظام إدارة سوق جرزونة</p>
                </div>
            </div>
        `;
    }
};

// تصدير الوظائف للاستخدام في ملفات أخرى
window.DateUtils = DateUtils;
window.NumberUtils = NumberUtils;
window.ValidationUtils = ValidationUtils;
window.StorageUtils = StorageUtils;
window.DOMUtils = DOMUtils;
window.NotificationUtils = NotificationUtils;
window.ExportUtils = ExportUtils;
window.SearchUtils = SearchUtils;
window.PrintUtils = PrintUtils;
