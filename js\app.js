// Main Application Controller for Jerzouna Wholesale Market
// Author: <PERSON><PERSON><PERSON> Souli

/**
 * التطبيق الرئيسي لسوق الجملة للخضر والغلال بجرزونة
 */
class JerzounaApp {
    constructor() {
        this.currentSection = 'dashboard';
        this.currentDate = new Date();
        this.initializeApp();
    }

    /**
     * تهيئة التطبيق
     */
    initializeApp() {
        this.setupEventListeners();
        this.updateDateTime();
        this.updateDashboardStats();
        this.loadRecentActivities();
        this.setupDatePickers();
        
        // تحديث الوقت كل ثانية
        setInterval(() => this.updateDateTime(), 1000);
        
        // تحديث الإحصائيات كل دقيقة
        setInterval(() => this.updateDashboardStats(), 60000);

        // إظهار رسالة ترحيب
        setTimeout(() => {
            NotificationUtils.showSuccess('مرحباً بك في نظام إدارة سوق الجملة للخضر والغلال بجرزونة');
        }, 1000);
    }

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners() {
        // التنقل بين الأقسام
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                const section = item.dataset.section;
                this.navigateToSection(section);
            });
        });

        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });

        // إغلاق النوافذ المنبثقة عند النقر خارجها
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.closeModal(e.target.id);
            }
        });

        // أزرار إغلاق النوافذ المنبثقة
        const closeButtons = document.querySelectorAll('.modal .close');
        closeButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const modal = e.target.closest('.modal');
                if (modal) {
                    this.closeModal(modal.id);
                }
            });
        });
    }

    /**
     * التنقل إلى قسم معين
     */
    navigateToSection(sectionName) {
        // إخفاء جميع الأقسام
        const sections = document.querySelectorAll('.content-section');
        sections.forEach(section => {
            section.classList.remove('active');
        });

        // إزالة الفئة النشطة من جميع عناصر التنقل
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            item.classList.remove('active');
        });

        // إظهار القسم المطلوب
        const targetSection = document.getElementById(sectionName);
        if (targetSection) {
            targetSection.classList.add('active');
        }

        // تفعيل عنصر التنقل المطلوب
        const targetNavItem = document.querySelector(`[data-section="${sectionName}"]`);
        if (targetNavItem) {
            targetNavItem.classList.add('active');
        }

        this.currentSection = sectionName;

        // تحديث البيانات حسب القسم
        this.loadSectionData(sectionName);
    }

    /**
     * تحميل بيانات القسم
     */
    loadSectionData(sectionName) {
        switch (sectionName) {
            case 'dashboard':
                this.updateDashboardStats();
                this.loadRecentActivities();
                break;
            case 'customers':
                if (window.customersManager) {
                    window.customersManager.loadCustomers();
                }
                break;
            case 'products':
                if (window.productsManager) {
                    window.productsManager.loadProducts();
                }
                break;
            case 'suppliers':
                if (window.suppliersManager) {
                    window.suppliersManager.loadSuppliers();
                }
                break;
            case 'boxes':
                if (window.boxesManager) {
                    window.boxesManager.loadBoxTypes();
                }
                break;
            case 'purchases':
                if (window.purchasesManager) {
                    window.purchasesManager.loadPurchases();
                }
                break;
            case 'supplier-invoices':
                if (window.invoicesManager) {
                    window.invoicesManager.loadInvoices();
                }
                break;
            case 'receipts':
                if (window.receiptsManager) {
                    window.receiptsManager.loadReceipts();
                }
                break;
        }
    }

    /**
     * معالجة اختصارات لوحة المفاتيح
     */
    handleKeyboardShortcuts(e) {
        // Ctrl + 1-8 للتنقل بين الأقسام
        if (e.ctrlKey && e.key >= '1' && e.key <= '8') {
            e.preventDefault();
            const sections = ['dashboard', 'customers', 'products', 'suppliers', 'boxes', 'purchases', 'supplier-invoices', 'receipts'];
            const index = parseInt(e.key) - 1;
            if (sections[index]) {
                this.navigateToSection(sections[index]);
            }
        }

        // ESC لإغلاق النوافذ المنبثقة
        if (e.key === 'Escape') {
            const openModals = document.querySelectorAll('.modal[style*="block"]');
            openModals.forEach(modal => {
                this.closeModal(modal.id);
            });
        }

        // F5 لتحديث البيانات
        if (e.key === 'F5') {
            e.preventDefault();
            this.refreshCurrentSection();
        }
    }

    /**
     * تحديث التاريخ والوقت
     */
    updateDateTime() {
        const now = new Date();
        const dateElement = document.getElementById('currentDate');
        const timeElement = document.getElementById('currentTime');

        if (dateElement) {
            dateElement.textContent = DateUtils.formatDate(now);
        }

        if (timeElement) {
            timeElement.textContent = DateUtils.formatTime(now);
        }
    }

    /**
     * تحديث إحصائيات لوحة التحكم
     */
    updateDashboardStats() {
        const stats = database.getStatistics();

        // تحديث الإحصائيات
        this.updateStatElement('totalCustomers', stats.totalCustomers);
        this.updateStatElement('totalProducts', stats.totalProducts);
        this.updateStatElement('totalSuppliers', stats.totalSuppliers);
        this.updateStatElement('todayPurchases', stats.todayPurchases || 0);
        this.updateStatElement('totalBoxes', stats.totalBoxes || 0);
        this.updateStatElement('todayRevenue', NumberUtils.formatNumber(stats.todayRevenue || 0));
    }

    /**
     * تحديث عنصر إحصائي
     */
    updateStatElement(elementId, value) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = value;
        }
    }

    /**
     * تحميل الأنشطة الحديثة
     */
    loadRecentActivities() {
        const activitiesContainer = document.getElementById('recentActivities');
        if (!activitiesContainer) return;

        const activities = database.getRecentActivities(5);
        activitiesContainer.innerHTML = '';

        if (activities.length === 0) {
            activitiesContainer.innerHTML = `
                <div class="no-data">
                    <i class="fas fa-info-circle"></i>
                    <p>لا توجد أنشطة حديثة</p>
                </div>
            `;
            return;
        }

        activities.forEach(activity => {
            const activityElement = document.createElement('div');
            activityElement.className = 'activity-item';
            activityElement.innerHTML = `
                <div class="activity-icon">
                    <i class="fas fa-${this.getActivityIcon(activity.type)}"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">${activity.description}</div>
                    <div class="activity-time">${DateUtils.formatDateTime(new Date(activity.timestamp))}</div>
                </div>
            `;
            activitiesContainer.appendChild(activityElement);
        });
    }

    /**
     * الحصول على أيقونة النشاط
     */
    getActivityIcon(type) {
        const icons = {
            system: 'cog',
            customer: 'user',
            product: 'apple-alt',
            supplier: 'truck',
            purchase: 'shopping-cart',
            invoice: 'file-invoice',
            receipt: 'receipt',
            box: 'box'
        };
        return icons[type] || 'info-circle';
    }

    /**
     * إعداد منتقيات التاريخ
     */
    setupDatePickers() {
        const today = new Date().toISOString().split('T')[0];
        
        // تعيين التاريخ الحالي لجميع منتقيات التاريخ
        const datePickers = document.querySelectorAll('.date-picker');
        datePickers.forEach(picker => {
            picker.value = today;
            picker.addEventListener('change', (e) => {
                this.handleDateChange(e.target);
            });
        });
    }

    /**
     * معالجة تغيير التاريخ
     */
    handleDateChange(datePickerElement) {
        const selectedDate = new Date(datePickerElement.value);
        const sectionId = datePickerElement.id.replace('DatePicker', '');
        
        // تحديث البيانات حسب التاريخ المحدد
        this.loadDataForDate(sectionId, selectedDate);
    }

    /**
     * تحميل البيانات لتاريخ محدد
     */
    loadDataForDate(section, date) {
        switch (section) {
            case 'dashboard':
                this.updateDashboardStats();
                break;
            case 'products':
                if (window.productsManager) {
                    window.productsManager.filterByDate(date);
                }
                break;
            case 'purchases':
                if (window.purchasesManager) {
                    window.purchasesManager.filterByDate(date);
                }
                break;
            case 'invoices':
                if (window.invoicesManager) {
                    window.invoicesManager.filterByDate(date);
                }
                break;
            case 'receipts':
                if (window.receiptsManager) {
                    window.receiptsManager.filterByDate(date);
                }
                break;
        }
    }

    /**
     * فتح نافذة منبثقة
     */
    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
        }
    }

    /**
     * إغلاق نافذة منبثقة
     */
    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';

            // مسح النماذج
            const forms = modal.querySelectorAll('form');
            forms.forEach(form => form.reset());
        }
    }

    /**
     * تحديث القسم الحالي
     */
    refreshCurrentSection() {
        this.loadSectionData(this.currentSection);
        NotificationUtils.showInfo('تم تحديث البيانات');
    }

    /**
     * البحث الذكي
     */
    setupSmartSearch(inputElement, dataSource, onSelect) {
        let searchTimeout;

        inputElement.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            const query = e.target.value.trim();

            if (query.length < 2) {
                this.hideSuggestions(inputElement);
                return;
            }

            searchTimeout = setTimeout(() => {
                this.showSuggestions(inputElement, dataSource, query, onSelect);
            }, 300);
        });

        // إخفاء الاقتراحات عند فقدان التركيز
        inputElement.addEventListener('blur', (e) => {
            setTimeout(() => {
                this.hideSuggestions(inputElement);
            }, 200);
        });
    }

    /**
     * إظهار الاقتراحات
     */
    showSuggestions(inputElement, dataSource, query, onSelect) {
        const container = inputElement.parentElement;
        let suggestionsDiv = container.querySelector('.suggestions-dropdown');

        if (!suggestionsDiv) {
            suggestionsDiv = document.createElement('div');
            suggestionsDiv.className = 'suggestions-dropdown';
            container.appendChild(suggestionsDiv);
        }

        // البحث في البيانات
        const results = dataSource.filter(item =>
            item.name.toLowerCase().includes(query.toLowerCase()) ||
            (item.customerId && item.customerId.toLowerCase().includes(query.toLowerCase())) ||
            (item.supplierId && item.supplierId.toLowerCase().includes(query.toLowerCase()))
        ).slice(0, 5);

        if (results.length === 0) {
            suggestionsDiv.style.display = 'none';
            return;
        }

        suggestionsDiv.innerHTML = '';
        results.forEach(item => {
            const suggestionItem = document.createElement('div');
            suggestionItem.className = 'suggestion-item';
            suggestionItem.textContent = item.name;
            suggestionItem.addEventListener('click', () => {
                onSelect(item);
                this.hideSuggestions(inputElement);
            });
            suggestionsDiv.appendChild(suggestionItem);
        });

        suggestionsDiv.style.display = 'block';
    }

    /**
     * إخفاء الاقتراحات
     */
    hideSuggestions(inputElement) {
        const container = inputElement.parentElement;
        const suggestionsDiv = container.querySelector('.suggestions-dropdown');
        if (suggestionsDiv) {
            suggestionsDiv.style.display = 'none';
        }
    }

    /**
     * حساب الوزن الصافي
     */
    calculateNetWeight(grossWeight, boxType) {
        const boxTypes = database.getBoxTypes();
        const box = boxTypes.find(b => b.name === boxType);

        if (!box) return grossWeight;

        if (box.name === 'Carton') {
            // للكرتون، الوزن يُدخل يدوياً
            return grossWeight;
        }

        return Math.max(0, grossWeight - box.emptyWeight);
    }

    /**
     * حساب الرهن
     */
    calculateMortgage(boxCount, boxType) {
        const boxTypes = database.getBoxTypes();
        const box = boxTypes.find(b => b.name === boxType);

        if (!box || !box.mortgage) return 0;

        return boxCount * box.mortgage;
    }

    /**
     * حساب سعر الحمولة
     */
    calculateLoadPrice(boxCount, boxType, netWeight = 0) {
        const boxTypes = database.getBoxTypes();
        const box = boxTypes.find(b => b.name === boxType);

        if (!box) return 0;

        if (box.name === 'بلا حمولة') {
            return netWeight * 10;
        }

        return boxCount * box.loadPrice;
    }

    /**
     * تصدير البيانات
     */
    exportData() {
        const backup = database.createBackup();
        const filename = `jerzouna_backup_${new Date().toISOString().split('T')[0]}.json`;
        ExportUtils.exportToJSON(backup, filename);
        NotificationUtils.showSuccess('تم تصدير البيانات بنجاح');
    }

    /**
     * استيراد البيانات
     */
    importData(file) {
        ExportUtils.importFromJSON(file)
            .then(data => {
                if (database.restoreFromBackup(data)) {
                    NotificationUtils.showSuccess('تم استيراد البيانات بنجاح');
                    this.refreshCurrentSection();
                } else {
                    NotificationUtils.showError('فشل في استيراد البيانات');
                }
            })
            .catch(error => {
                NotificationUtils.showError('ملف غير صالح');
            });
    }
}

// إنشاء مثيل التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.app = new JerzounaApp();
});

// تصدير التطبيق للاستخدام في ملفات أخرى
window.JerzounaApp = JerzounaApp;
