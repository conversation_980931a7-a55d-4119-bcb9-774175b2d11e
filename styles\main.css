/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: #e8e8e8;
    line-height: 1.6;
    direction: rtl;
    min-height: 100vh;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, #0f1419 0%, #1a1a2e 100%);
    color: #e8e8e8;
    padding: 1rem 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    position: sticky;
    top: 0;
    z-index: 1000;
    border-bottom: 2px solid #00d4ff;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.logo {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo i {
    font-size: 2.5rem;
    color: #00d4ff;
}

.logo h1 {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
}

.subtitle {
    font-size: 0.9rem;
    color: #00d4ff;
    margin-right: 1rem;
    font-weight: 500;
}

.header-info {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.date-time {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 0.9rem;
}

.owner-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

/* Main Container */
.main-container {
    display: flex;
    max-width: 1400px;
    margin: 0 auto;
    min-height: calc(100vh - 80px);
    background: #1e1e2e;
    border-radius: 15px 15px 0 0;
    overflow: hidden;
    box-shadow: 0 -5px 30px rgba(0,0,0,0.3);
    border: 1px solid #2a2a3a;
}

/* Sidebar */
.sidebar {
    width: 280px;
    background: linear-gradient(180deg, #0f1419 0%, #1a1a2e 100%);
    color: #e8e8e8;
    padding: 2rem 0;
    box-shadow: 4px 0 20px rgba(0,0,0,0.3);
    border-left: 2px solid #00d4ff;
}

.sidebar-nav ul {
    list-style: none;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border-right: 4px solid transparent;
}

.nav-item:hover {
    background: rgba(0, 212, 255, 0.1);
    border-right-color: #00d4ff;
    transform: translateX(-5px);
}

.nav-item.active {
    background: rgba(0, 212, 255, 0.2);
    border-right-color: #00d4ff;
    color: #00d4ff;
    transform: translateX(-5px);
}

.nav-item i {
    font-size: 1.2rem;
    width: 20px;
}

.nav-item span {
    font-weight: 500;
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 2rem;
    background: #1e1e2e;
    overflow-y: auto;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.section-header h2 {
    font-size: 1.8rem;
    color: #00d4ff;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Section Controls */
.section-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Date Picker */
.date-picker-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.date-picker-container label {
    color: #e8e8e8;
    font-weight: 500;
    white-space: nowrap;
}

.date-picker {
    padding: 0.5rem;
    border: 2px solid #2a2a3a;
    border-radius: 6px;
    background: #2a2a3a;
    color: #e8e8e8;
    font-family: inherit;
    transition: border-color 0.3s ease;
}

.date-picker:focus {
    outline: none;
    border-color: #00d4ff;
}

/* Search Container */
.search-container {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input {
    padding: 0.75rem 2.5rem 0.75rem 1rem;
    border: 2px solid #2a2a3a;
    border-radius: 25px;
    background: #2a2a3a;
    color: #e8e8e8;
    font-family: inherit;
    transition: all 0.3s ease;
    min-width: 250px;
}

.search-input:focus {
    outline: none;
    border-color: #00d4ff;
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
}

.search-input::placeholder {
    color: #888;
}

.search-icon {
    position: absolute;
    left: 1rem;
    color: #888;
    pointer-events: none;
}

/* Autocomplete */
.autocomplete-container {
    position: relative;
}

.suggestions-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #2a2a3a;
    border: 1px solid #3a3a4a;
    border-radius: 6px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.suggestion-item {
    padding: 0.75rem;
    cursor: pointer;
    border-bottom: 1px solid #3a3a4a;
    transition: background-color 0.2s ease;
}

.suggestion-item:hover,
.suggestion-item.active {
    background: #00d4ff;
    color: #1e1e2e;
}

.suggestion-item:last-child {
    border-bottom: none;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-family: inherit;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.btn-primary {
    background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
    color: #1e1e2e;
    font-weight: 600;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 212, 255, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    color: #1e1e2e;
    font-weight: 600;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* Cards */
.card, .dashboard-card, .customers-table-card, .products-table-card,
.suppliers-table-card, .boxes-table-card, .purchase-form-card,
.purchases-table-card, .invoices-list-card, .receipts-table-card {
    background: #2a2a3a;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    overflow: hidden;
    margin-bottom: 2rem;
    transition: transform 0.3s ease;
    border: 1px solid #3a3a4a;
}

.card:hover, .dashboard-card:hover, .report-card:hover {
    transform: translateY(-5px);
}

.card-header {
    background: linear-gradient(135deg, #1a1a2e 0%, #2a2a3a 100%);
    padding: 1.5rem;
    border-bottom: 1px solid #3a3a4a;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    color: #00d4ff;
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.card-content {
    padding: 1.5rem;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    display: flex;
    align-items: center;
    gap: 1.5rem;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.stat-content p {
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

/* Forms */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-family: inherit;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3498db;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

/* Sales Form */
.sales-container {
    display: grid;
    gap: 2rem;
}

.product-input {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr auto;
    gap: 1rem;
    align-items: end;
    margin-bottom: 1rem;
}

.invoice-items {
    margin: 1.5rem 0;
}

.invoice-total {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    margin-top: 1rem;
}

.total-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.final-total {
    font-size: 1.2rem;
    font-weight: 700;
    color: #2c3e50;
    border-top: 2px solid #dee2e6;
    padding-top: 0.5rem;
    margin-top: 1rem;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
}

/* Tables */
.data-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
}

.data-table th,
.data-table td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid #e9ecef;
}

.data-table th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    font-weight: 600;
    color: #2c3e50;
}

.data-table tbody tr:hover {
    background: #f8f9fa;
}

/* Inventory Grid */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.product-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.product-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.product-name {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
}

.product-category {
    background: #3498db;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
}

.product-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.info-item {
    text-align: center;
}

.info-label {
    font-size: 0.8rem;
    color: #7f8c8d;
    margin-bottom: 0.25rem;
}

.info-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
}

.product-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

/* Stock Status */
.stock-status {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-align: center;
    margin-bottom: 1rem;
}

.stock-good {
    background: #d4edda;
    color: #155724;
}

.stock-low {
    background: #fff3cd;
    color: #856404;
}

.stock-out {
    background: #f8d7da;
    color: #721c24;
}

/* Reports */
.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.chart-container {
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 8px;
    color: #7f8c8d;
}

/* Settings */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

/* Modals */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 10px 50px rgba(0,0,0,0.3);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.modal-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 1.5rem;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    color: #2c3e50;
    font-size: 1.2rem;
    font-weight: 600;
}

.close {
    font-size: 1.5rem;
    cursor: pointer;
    color: #7f8c8d;
    transition: color 0.3s ease;
}

.close:hover {
    color: #e74c3c;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #dee2e6;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

/* Activities and Alerts */
.activities-list,
.alerts-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item,
.alert-item {
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.activity-item:last-child,
.alert-item:last-child {
    border-bottom: none;
}

.activity-icon,
.alert-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.9rem;
}

.activity-icon {
    background: #3498db;
}

.alert-icon {
    background: #e74c3c;
}

.activity-content,
.alert-content {
    flex: 1;
}

.activity-title,
.alert-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.activity-time,
.alert-time {
    font-size: 0.8rem;
    color: #7f8c8d;
}

/* Filter Controls */
.filter-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.filter-controls select {
    padding: 0.5rem;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    background: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-container {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        padding: 1rem 0;
    }
    
    .sidebar-nav ul {
        display: flex;
        overflow-x: auto;
        padding: 0 1rem;
    }
    
    .nav-item {
        min-width: 120px;
        flex-direction: column;
        text-align: center;
        padding: 1rem;
        border-right: none;
        border-bottom: 4px solid transparent;
    }
    
    .nav-item:hover,
    .nav-item.active {
        border-right: none;
        border-bottom-color: #3498db;
    }
    
    .main-content {
        padding: 1rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .product-input {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .header-info {
        flex-direction: column;
        gap: 0.5rem;
    }
}

/* Print Styles */
@media print {
    .sidebar,
    .section-header,
    .form-actions,
    .btn {
        display: none !important;
    }
    
    .main-container {
        box-shadow: none;
        border-radius: 0;
    }
    
    .main-content {
        padding: 0;
    }
    
    body {
        background: white;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.content-section.active {
    animation: fadeIn 0.3s ease;
}

/* Purchase Form Specific Styles */
.purchase-item-input {
    background: #1a1a2e;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    border: 1px solid #3a3a4a;
}

.purchase-items-table {
    margin: 1.5rem 0;
}

.purchase-summary {
    background: #1a1a2e;
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid #3a3a4a;
    margin-top: 1rem;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
    font-weight: 600;
}

.summary-row:last-child {
    margin-bottom: 0;
    color: #00d4ff;
    font-size: 1.2rem;
}

.checkbox-group {
    display: flex;
    align-items: center;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    margin-bottom: 0;
}

.checkbox-group input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.payment-section {
    background: #1a1a2e;
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid #3a3a4a;
    margin-top: 1rem;
}

/* Supplier Invoices Grid */
.suppliers-invoices-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.supplier-invoice-card {
    background: #1a1a2e;
    border: 1px solid #3a3a4a;
    border-radius: 8px;
    padding: 1.5rem;
    transition: transform 0.3s ease;
}

.supplier-invoice-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 212, 255, 0.2);
}

.supplier-invoice-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #3a3a4a;
}

.supplier-name {
    font-size: 1.2rem;
    font-weight: 600;
    color: #00d4ff;
}

.invoice-summary {
    margin-bottom: 1rem;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    color: #e8e8e8;
}

.summary-item strong {
    color: #00d4ff;
}

/* Action Icons */
.action-icons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.action-icon {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    font-size: 0.9rem;
}

.action-icon.debt {
    background: #ffc107;
    color: #1e1e2e;
}

.action-icon.boxes {
    background: #28a745;
    color: white;
}

.action-icon.mortgage {
    background: #dc3545;
    color: white;
}

.action-icon.view {
    background: #17a2b8;
    color: white;
}

.action-icon.edit {
    background: #6c757d;
    color: white;
}

.action-icon.delete {
    background: #dc3545;
    color: white;
}

.action-icon.print {
    background: #00d4ff;
    color: #1e1e2e;
}

.action-icon:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(0,0,0,0.3);
}

/* Status Badges */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-align: center;
}

.status-paid {
    background: #28a745;
    color: white;
}

.status-partial {
    background: #ffc107;
    color: #1e1e2e;
}

.status-unpaid {
    background: #dc3545;
    color: white;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #2a2a3a;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #00d4ff;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #0099cc;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
