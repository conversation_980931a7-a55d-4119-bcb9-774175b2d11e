// Customers Management for Jerzouna Wholesale Market
// Author: <PERSON>ussema Souli

/**
 * نظام إدارة الحرفاء
 */
class CustomersManager {
    constructor() {
        this.currentCustomers = [];
        this.filteredCustomers = [];
        this.initializeCustomersSystem();
    }

    /**
     * تهيئة نظام الحرفاء
     */
    initializeCustomersSystem() {
        this.setupEventListeners();
        this.loadCustomers();
        this.setupSmartSearch();
    }

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners() {
        // زر إضافة حريف جديد
        const addCustomerBtn = document.getElementById('addCustomerBtn');
        if (addCustomerBtn) {
            addCustomerBtn.addEventListener('click', () => this.openAddCustomerModal());
        }

        // نموذج إضافة/تعديل حريف
        const customerForm = document.getElementById('customerForm');
        if (customerForm) {
            customerForm.addEventListener('submit', (e) => this.saveCustomer(e));
        }

        // أزرار إلغاء وحفظ
        const saveCustomerBtn = document.getElementById('saveCustomerBtn');
        const cancelCustomerBtn = document.getElementById('cancelCustomerBtn');

        if (saveCustomerBtn) {
            saveCustomerBtn.addEventListener('click', () => this.saveCustomer());
        }

        if (cancelCustomerBtn) {
            cancelCustomerBtn.addEventListener('click', () => this.closeCustomerModal());
        }

        // البحث في الحرفاء
        const customerSearch = document.getElementById('customerSearch');
        if (customerSearch) {
            customerSearch.addEventListener('input', (e) => this.searchCustomers(e.target.value));
        }
    }

    /**
     * إعداد البحث الذكي
     */
    setupSmartSearch() {
        const customerSearch = document.getElementById('customerSearch');
        if (customerSearch && window.app) {
            window.app.setupSmartSearch(
                customerSearch,
                this.currentCustomers,
                (customer) => {
                    customerSearch.value = customer.name;
                    this.highlightCustomer(customer.id);
                }
            );
        }
    }

    /**
     * تحميل الحرفاء
     */
    loadCustomers() {
        this.currentCustomers = database.getCustomers();
        this.filteredCustomers = [...this.currentCustomers];
        this.renderCustomersTable();
    }

    /**
     * عرض جدول الحرفاء
     */
    renderCustomersTable() {
        const customersBody = document.getElementById('customersBody');
        if (!customersBody) return;

        customersBody.innerHTML = '';

        if (this.filteredCustomers.length === 0) {
            customersBody.innerHTML = `
                <tr>
                    <td colspan="8" style="text-align: center; color: #888; padding: 2rem;">
                        <i class="fas fa-users" style="font-size: 3rem; margin-bottom: 1rem; display: block;"></i>
                        لا توجد حرفاء مسجلين
                    </td>
                </tr>
            `;
            return;
        }

        this.filteredCustomers.forEach(customer => {
            const row = document.createElement('tr');
            row.id = `customer-${customer.id}`;
            row.innerHTML = `
                <td>${customer.customerId}</td>
                <td>${customer.name}</td>
                <td>${customer.phone || '-'}</td>
                <td>${customer.address || '-'}</td>
                <td>
                    <span class="debt-amount ${customer.totalDebt > 0 ? 'has-debt' : ''}">
                        ${NumberUtils.formatCurrency(customer.totalDebt || 0)}
                    </span>
                </td>
                <td>
                    <span class="boxes-count">
                        ${customer.totalBoxes || 0} صندوق
                    </span>
                </td>
                <td>
                    <span class="mortgage-amount ${customer.totalMortgage > 0 ? 'has-mortgage' : ''}">
                        ${NumberUtils.formatCurrency(customer.totalMortgage || 0)}
                    </span>
                </td>
                <td>
                    <div class="action-icons">
                        <button class="action-icon debt" title="الديون" onclick="customersManager.viewCustomerDebt('${customer.id}')">
                            <i class="fas fa-money-bill-wave"></i>
                        </button>
                        <button class="action-icon boxes" title="الصناديق" onclick="customersManager.viewCustomerBoxes('${customer.id}')">
                            <i class="fas fa-boxes"></i>
                        </button>
                        <button class="action-icon mortgage" title="الرهون" onclick="customersManager.viewCustomerMortgage('${customer.id}')">
                            <i class="fas fa-handshake"></i>
                        </button>
                        <button class="action-icon edit" title="تعديل" onclick="customersManager.editCustomer('${customer.id}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-icon delete" title="حذف" onclick="customersManager.deleteCustomer('${customer.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            customersBody.appendChild(row);
        });
    }

    /**
     * البحث في الحرفاء
     */
    searchCustomers(query) {
        if (!query.trim()) {
            this.filteredCustomers = [...this.currentCustomers];
        } else {
            this.filteredCustomers = this.currentCustomers.filter(customer =>
                customer.name.toLowerCase().includes(query.toLowerCase()) ||
                customer.customerId.toLowerCase().includes(query.toLowerCase()) ||
                (customer.phone && customer.phone.includes(query))
            );
        }
        this.renderCustomersTable();
    }

    /**
     * تمييز حريف في الجدول
     */
    highlightCustomer(customerId) {
        // إزالة التمييز السابق
        const previousHighlight = document.querySelector('.customer-highlighted');
        if (previousHighlight) {
            previousHighlight.classList.remove('customer-highlighted');
        }

        // تمييز الحريف الجديد
        const customerRow = document.getElementById(`customer-${customerId}`);
        if (customerRow) {
            customerRow.classList.add('customer-highlighted');
            customerRow.scrollIntoView({ behavior: 'smooth', block: 'center' });
            
            // إزالة التمييز بعد 3 ثوان
            setTimeout(() => {
                customerRow.classList.remove('customer-highlighted');
            }, 3000);
        }
    }

    /**
     * فتح نافذة إضافة حريف جديد
     */
    openAddCustomerModal() {
        this.clearCustomerForm();
        document.getElementById('customerModalTitle').textContent = 'إضافة حريف جديد';
        document.getElementById('customerIdInput').value = this.generateCustomerId();
        window.app.openModal('customerModal');
    }

    /**
     * تعديل حريف
     */
    editCustomer(customerId) {
        const customer = database.getCustomer(customerId);
        if (!customer) {
            NotificationUtils.showError('الحريف غير موجود');
            return;
        }

        this.fillCustomerForm(customer);
        document.getElementById('customerModalTitle').textContent = 'تعديل بيانات الحريف';
        window.app.openModal('customerModal');
    }

    /**
     * ملء نموذج الحريف
     */
    fillCustomerForm(customer) {
        document.getElementById('customerIdInput').value = customer.customerId;
        document.getElementById('customerNameModal').value = customer.name;
        document.getElementById('customerPhone').value = customer.phone || '';
        document.getElementById('customerAddress').value = customer.address || '';
        document.getElementById('customerNotes').value = customer.notes || '';
    }

    /**
     * مسح نموذج الحريف
     */
    clearCustomerForm() {
        document.getElementById('customerForm').reset();
        document.getElementById('customerIdInput').value = '';
    }

    /**
     * حفظ الحريف
     */
    saveCustomer(event) {
        if (event) {
            event.preventDefault();
        }

        const customerId = document.getElementById('customerIdInput').value;
        const name = document.getElementById('customerNameModal').value.trim();
        const phone = document.getElementById('customerPhone').value.trim();
        const address = document.getElementById('customerAddress').value.trim();
        const notes = document.getElementById('customerNotes').value.trim();

        // التحقق من صحة البيانات
        if (!name) {
            NotificationUtils.showError('يرجى إدخال اسم الحريف');
            return;
        }

        // التحقق من عدم تكرار الاسم
        const existingCustomer = this.currentCustomers.find(c => 
            c.name.toLowerCase() === name.toLowerCase() && c.customerId !== customerId
        );

        if (existingCustomer) {
            NotificationUtils.showError('يوجد حريف بنفس الاسم مسبقاً');
            return;
        }

        const customerData = {
            customerId: customerId || this.generateCustomerId(),
            name,
            phone,
            address,
            notes,
            totalDebt: 0,
            totalBoxes: 0,
            totalMortgage: 0
        };

        try {
            if (customerId && database.getCustomer(database.getCustomers().find(c => c.customerId === customerId)?.id)) {
                // تحديث حريف موجود
                const existingId = database.getCustomers().find(c => c.customerId === customerId)?.id;
                database.updateCustomer(existingId, customerData);
                NotificationUtils.showSuccess('تم تحديث بيانات الحريف بنجاح');
            } else {
                // إضافة حريف جديد
                database.addCustomer(customerData);
                NotificationUtils.showSuccess('تم إضافة الحريف بنجاح');
            }

            this.loadCustomers();
            this.closeCustomerModal();
            
            // تحديث إحصائيات لوحة التحكم
            if (window.app) {
                window.app.updateDashboardStats();
            }

        } catch (error) {
            console.error('خطأ في حفظ الحريف:', error);
            NotificationUtils.showError('حدث خطأ أثناء حفظ البيانات');
        }
    }

    /**
     * حذف حريف
     */
    deleteCustomer(customerId) {
        const customer = database.getCustomer(customerId);
        if (!customer) {
            NotificationUtils.showError('الحريف غير موجود');
            return;
        }

        if (!confirm(`هل أنت متأكد من حذف الحريف "${customer.name}"؟\n\nتحذير: سيتم حذف جميع البيانات المرتبطة بهذا الحريف.`)) {
            return;
        }

        try {
            database.deleteCustomer(customerId);
            NotificationUtils.showSuccess('تم حذف الحريف بنجاح');
            this.loadCustomers();
            
            // تحديث إحصائيات لوحة التحكم
            if (window.app) {
                window.app.updateDashboardStats();
            }

        } catch (error) {
            console.error('خطأ في حذف الحريف:', error);
            NotificationUtils.showError('حدث خطأ أثناء حذف الحريف');
        }
    }

    /**
     * عرض ديون الحريف
     */
    viewCustomerDebt(customerId) {
        const customer = database.getCustomer(customerId);
        if (!customer) {
            NotificationUtils.showError('الحريف غير موجود');
            return;
        }

        // يمكن إضافة نافذة منبثقة لعرض تفاصيل الديون
        alert(`ديون الحريف: ${customer.name}\n\nإجمالي الديون: ${NumberUtils.formatCurrency(customer.totalDebt || 0)}`);
    }

    /**
     * عرض صناديق الحريف
     */
    viewCustomerBoxes(customerId) {
        const customer = database.getCustomer(customerId);
        if (!customer) {
            NotificationUtils.showError('الحريف غير موجود');
            return;
        }

        // يمكن إضافة نافذة منبثقة لعرض تفاصيل الصناديق
        alert(`صناديق الحريف: ${customer.name}\n\nإجمالي الصناديق: ${customer.totalBoxes || 0} صندوق`);
    }

    /**
     * عرض رهون الحريف
     */
    viewCustomerMortgage(customerId) {
        const customer = database.getCustomer(customerId);
        if (!customer) {
            NotificationUtils.showError('الحريف غير موجود');
            return;
        }

        // يمكن إضافة نافذة منبثقة لعرض تفاصيل الرهون
        alert(`رهون الحريف: ${customer.name}\n\nإجمالي الرهون: ${NumberUtils.formatCurrency(customer.totalMortgage || 0)}`);
    }

    /**
     * إغلاق نافذة الحريف
     */
    closeCustomerModal() {
        window.app.closeModal('customerModal');
    }

    /**
     * توليد رقم تعريف جديد للحريف
     */
    generateCustomerId() {
        const customers = database.getCustomers();
        const maxId = customers.reduce((max, customer) => {
            const num = parseInt(customer.customerId.replace('C', ''));
            return num > max ? num : max;
        }, 0);
        
        return `C${String(maxId + 1).padStart(3, '0')}`;
    }

    /**
     * الحصول على حريف بالاسم
     */
    getCustomerByName(name) {
        return this.currentCustomers.find(customer => 
            customer.name.toLowerCase() === name.toLowerCase()
        );
    }

    /**
     * الحصول على حريف برقم التعريف
     */
    getCustomerById(customerId) {
        return this.currentCustomers.find(customer => 
            customer.customerId === customerId
        );
    }
}

// إنشاء مثيل من مدير الحرفاء
const customersManager = new CustomersManager();

// تصدير مدير الحرفاء للاستخدام في ملفات أخرى
window.customersManager = customersManager;
