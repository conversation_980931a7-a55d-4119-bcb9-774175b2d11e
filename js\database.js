// Database Management for Jerzouna Market POS System
// Author: <PERSON>ussema Souli

/**
 * نظام إدارة قاعدة البيانات المحلية
 */
class Database {
    constructor() {
        this.storagePrefix = 'jerzouna_market_';
        this.initializeDatabase();
    }

    /**
     * تهيئة قاعدة البيانات
     */
    initializeDatabase() {
        // إنشاء الجداول الأساسية إذا لم تكن موجودة
        this.initializeTable('products', []);
        this.initializeTable('customers', []);
        this.initializeTable('suppliers', []);
        this.initializeTable('sales', []);
        this.initializeTable('settings', this.getDefaultSettings());
        this.initializeTable('activities', []);
        
        // إضافة بيانات تجريبية إذا كانت قاعدة البيانات فارغة
        if (this.getProducts().length === 0) {
            this.seedDatabase();
        }
    }

    /**
     * تهيئة جدول معين
     */
    initializeTable(tableName, defaultData) {
        const key = this.storagePrefix + tableName;
        if (!localStorage.getItem(key)) {
            StorageUtils.save(key, defaultData);
        }
    }

    /**
     * الحصول على الإعدادات الافتراضية
     */
    getDefaultSettings() {
        return {
            storeName: 'سوق الجملة للخضر والغلال بجرزونة',
            posNumber: '14',
            ownerName: 'بيه الغالي',
            taxRate: 19,
            currency: 'دينار',
            lowStockThreshold: 10,
            autoBackup: true,
            printAfterSale: true,
            theme: 'default'
        };
    }

    /**
     * إضافة بيانات تجريبية
     */
    seedDatabase() {
        // منتجات تجريبية
        const sampleProducts = [
            {
                id: this.generateId(),
                name: 'طماطم',
                category: 'خضروات',
                unit: 'كيلو',
                purchasePrice: 2.50,
                salePrice: 3.00,
                currentStock: 50,
                minStock: 10,
                createdAt: new Date().toISOString()
            },
            {
                id: this.generateId(),
                name: 'بطاطس',
                category: 'خضروات',
                unit: 'كيلو',
                purchasePrice: 1.80,
                salePrice: 2.20,
                currentStock: 75,
                minStock: 15,
                createdAt: new Date().toISOString()
            },
            {
                id: this.generateId(),
                name: 'تفاح أحمر',
                category: 'فواكه',
                unit: 'كيلو',
                purchasePrice: 4.00,
                salePrice: 5.00,
                currentStock: 30,
                minStock: 8,
                createdAt: new Date().toISOString()
            },
            {
                id: this.generateId(),
                name: 'موز',
                category: 'فواكه',
                unit: 'كيلو',
                purchasePrice: 3.50,
                salePrice: 4.20,
                currentStock: 25,
                minStock: 5,
                createdAt: new Date().toISOString()
            },
            {
                id: this.generateId(),
                name: 'أرز',
                category: 'حبوب',
                unit: 'كيس',
                purchasePrice: 15.00,
                salePrice: 18.00,
                currentStock: 20,
                minStock: 5,
                createdAt: new Date().toISOString()
            }
        ];

        // عملاء تجريبيون
        const sampleCustomers = [
            {
                id: this.generateId(),
                name: 'أحمد بن علي',
                phone: '98765432',
                address: 'حي النصر، جرزونة',
                totalPurchases: 0,
                createdAt: new Date().toISOString()
            },
            {
                id: this.generateId(),
                name: 'فاطمة الزهراء',
                phone: '97654321',
                address: 'حي السلام، جرزونة',
                totalPurchases: 0,
                createdAt: new Date().toISOString()
            },
            {
                id: this.generateId(),
                name: 'محمد الطاهر',
                phone: '96543210',
                address: 'وسط المدينة، جرزونة',
                totalPurchases: 0,
                createdAt: new Date().toISOString()
            }
        ];

        // موردون تجريبيون
        const sampleSuppliers = [
            {
                id: this.generateId(),
                name: 'مزرعة الخير',
                phone: '71234567',
                address: 'منطقة الزراعة، جرزونة',
                products: 'طماطم، بطاطس، خيار، فلفل',
                createdAt: new Date().toISOString()
            },
            {
                id: this.generateId(),
                name: 'بستان الفواكه',
                phone: '72345678',
                address: 'طريق القيروان، جرزونة',
                products: 'تفاح، موز، برتقال، عنب',
                createdAt: new Date().toISOString()
            }
        ];

        // حفظ البيانات التجريبية
        this.saveProducts(sampleProducts);
        this.saveCustomers(sampleCustomers);
        this.saveSuppliers(sampleSuppliers);

        // إضافة نشاط تهيئة النظام
        this.addActivity('تم تهيئة النظام بنجاح', 'system');
    }

    /**
     * توليد معرف فريد
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    /**
     * توليد رقم فاتورة
     */
    generateInvoiceNumber() {
        const today = new Date();
        const dateStr = today.toISOString().slice(0, 10).replace(/-/g, '');
        const sales = this.getSales();
        const todaySales = sales.filter(sale => 
            DateUtils.isToday(new Date(sale.date))
        );
        const sequenceNumber = (todaySales.length + 1).toString().padStart(3, '0');
        return `INV-${dateStr}-${sequenceNumber}`;
    }

    // ==================== إدارة المنتجات ====================

    /**
     * الحصول على جميع المنتجات
     */
    getProducts() {
        return StorageUtils.load(this.storagePrefix + 'products', []);
    }

    /**
     * الحصول على منتج بالمعرف
     */
    getProduct(id) {
        const products = this.getProducts();
        return products.find(product => product.id === id);
    }

    /**
     * إضافة منتج جديد
     */
    addProduct(productData) {
        const products = this.getProducts();
        const newProduct = {
            id: this.generateId(),
            ...productData,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        products.push(newProduct);
        this.saveProducts(products);
        this.addActivity(`تم إضافة منتج جديد: ${newProduct.name}`, 'product');
        return newProduct;
    }

    /**
     * تحديث منتج
     */
    updateProduct(id, productData) {
        const products = this.getProducts();
        const index = products.findIndex(product => product.id === id);
        if (index !== -1) {
            products[index] = {
                ...products[index],
                ...productData,
                updatedAt: new Date().toISOString()
            };
            this.saveProducts(products);
            this.addActivity(`تم تحديث المنتج: ${products[index].name}`, 'product');
            return products[index];
        }
        return null;
    }

    /**
     * حذف منتج
     */
    deleteProduct(id) {
        const products = this.getProducts();
        const index = products.findIndex(product => product.id === id);
        if (index !== -1) {
            const deletedProduct = products[index];
            products.splice(index, 1);
            this.saveProducts(products);
            this.addActivity(`تم حذف المنتج: ${deletedProduct.name}`, 'product');
            return true;
        }
        return false;
    }

    /**
     * تحديث مخزون منتج
     */
    updateProductStock(id, quantity, operation = 'subtract') {
        const product = this.getProduct(id);
        if (product) {
            const newStock = operation === 'add' 
                ? product.currentStock + quantity 
                : product.currentStock - quantity;
            
            return this.updateProduct(id, { currentStock: Math.max(0, newStock) });
        }
        return null;
    }

    /**
     * الحصول على المنتجات منخفضة المخزون
     */
    getLowStockProducts() {
        const products = this.getProducts();
        return products.filter(product => product.currentStock <= product.minStock);
    }

    /**
     * حفظ المنتجات
     */
    saveProducts(products) {
        return StorageUtils.save(this.storagePrefix + 'products', products);
    }

    // ==================== إدارة العملاء ====================

    /**
     * الحصول على جميع العملاء
     */
    getCustomers() {
        return StorageUtils.load(this.storagePrefix + 'customers', []);
    }

    /**
     * الحصول على عميل بالمعرف
     */
    getCustomer(id) {
        const customers = this.getCustomers();
        return customers.find(customer => customer.id === id);
    }

    /**
     * إضافة عميل جديد
     */
    addCustomer(customerData) {
        const customers = this.getCustomers();
        const newCustomer = {
            id: this.generateId(),
            ...customerData,
            totalPurchases: 0,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        customers.push(newCustomer);
        this.saveCustomers(customers);
        this.addActivity(`تم إضافة عميل جديد: ${newCustomer.name}`, 'customer');
        return newCustomer;
    }

    /**
     * تحديث عميل
     */
    updateCustomer(id, customerData) {
        const customers = this.getCustomers();
        const index = customers.findIndex(customer => customer.id === id);
        if (index !== -1) {
            customers[index] = {
                ...customers[index],
                ...customerData,
                updatedAt: new Date().toISOString()
            };
            this.saveCustomers(customers);
            this.addActivity(`تم تحديث العميل: ${customers[index].name}`, 'customer');
            return customers[index];
        }
        return null;
    }

    /**
     * حذف عميل
     */
    deleteCustomer(id) {
        const customers = this.getCustomers();
        const index = customers.findIndex(customer => customer.id === id);
        if (index !== -1) {
            const deletedCustomer = customers[index];
            customers.splice(index, 1);
            this.saveCustomers(customers);
            this.addActivity(`تم حذف العميل: ${deletedCustomer.name}`, 'customer');
            return true;
        }
        return false;
    }

    /**
     * تحديث إجمالي مشتريات العميل
     */
    updateCustomerPurchases(id, amount) {
        const customer = this.getCustomer(id);
        if (customer) {
            const newTotal = customer.totalPurchases + amount;
            return this.updateCustomer(id, { totalPurchases: newTotal });
        }
        return null;
    }

    /**
     * حفظ العملاء
     */
    saveCustomers(customers) {
        return StorageUtils.save(this.storagePrefix + 'customers', customers);
    }

    // ==================== إدارة الموردين ====================

    /**
     * الحصول على جميع الموردين
     */
    getSuppliers() {
        return StorageUtils.load(this.storagePrefix + 'suppliers', []);
    }

    /**
     * الحصول على مورد بالمعرف
     */
    getSupplier(id) {
        const suppliers = this.getSuppliers();
        return suppliers.find(supplier => supplier.id === id);
    }

    /**
     * إضافة مورد جديد
     */
    addSupplier(supplierData) {
        const suppliers = this.getSuppliers();
        const newSupplier = {
            id: this.generateId(),
            ...supplierData,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        suppliers.push(newSupplier);
        this.saveSuppliers(suppliers);
        this.addActivity(`تم إضافة مورد جديد: ${newSupplier.name}`, 'supplier');
        return newSupplier;
    }

    /**
     * تحديث مورد
     */
    updateSupplier(id, supplierData) {
        const suppliers = this.getSuppliers();
        const index = suppliers.findIndex(supplier => supplier.id === id);
        if (index !== -1) {
            suppliers[index] = {
                ...suppliers[index],
                ...supplierData,
                updatedAt: new Date().toISOString()
            };
            this.saveSuppliers(suppliers);
            this.addActivity(`تم تحديث المورد: ${suppliers[index].name}`, 'supplier');
            return suppliers[index];
        }
        return null;
    }

    /**
     * حذف مورد
     */
    deleteSupplier(id) {
        const suppliers = this.getSuppliers();
        const index = suppliers.findIndex(supplier => supplier.id === id);
        if (index !== -1) {
            const deletedSupplier = suppliers[index];
            suppliers.splice(index, 1);
            this.saveSuppliers(suppliers);
            this.addActivity(`تم حذف المورد: ${deletedSupplier.name}`, 'supplier');
            return true;
        }
        return false;
    }

    /**
     * حفظ الموردين
     */
    saveSuppliers(suppliers) {
        return StorageUtils.save(this.storagePrefix + 'suppliers', suppliers);
    }

    // ==================== إدارة المبيعات ====================

    /**
     * الحصول على جميع المبيعات
     */
    getSales() {
        return StorageUtils.load(this.storagePrefix + 'sales', []);
    }

    /**
     * الحصول على مبيعة بالمعرف
     */
    getSale(id) {
        const sales = this.getSales();
        return sales.find(sale => sale.id === id);
    }

    /**
     * إضافة مبيعة جديدة
     */
    addSale(saleData) {
        const sales = this.getSales();
        const newSale = {
            id: this.generateId(),
            number: this.generateInvoiceNumber(),
            ...saleData,
            date: new Date().toISOString(),
            createdAt: new Date().toISOString()
        };

        // تحديث مخزون المنتجات
        newSale.items.forEach(item => {
            this.updateProductStock(item.productId, item.quantity, 'subtract');
        });

        // تحديث إجمالي مشتريات العميل
        if (newSale.customerId) {
            this.updateCustomerPurchases(newSale.customerId, newSale.total);
        }

        sales.push(newSale);
        this.saveSales(sales);
        this.addActivity(`تم إنشاء فاتورة جديدة: ${newSale.number}`, 'sale');
        return newSale;
    }

    /**
     * الحصول على مبيعات اليوم
     */
    getTodaySales() {
        const sales = this.getSales();
        return sales.filter(sale => DateUtils.isToday(new Date(sale.date)));
    }

    /**
     * الحصول على مبيعات الأمس
     */
    getYesterdaySales() {
        const sales = this.getSales();
        return sales.filter(sale => DateUtils.isYesterday(new Date(sale.date)));
    }

    /**
     * الحصول على مبيعات فترة معينة
     */
    getSalesByDateRange(startDate, endDate) {
        const sales = this.getSales();
        return sales.filter(sale => {
            const saleDate = new Date(sale.date);
            return saleDate >= startDate && saleDate <= endDate;
        });
    }

    /**
     * حساب إجمالي مبيعات اليوم
     */
    getTodayTotalSales() {
        const todaySales = this.getTodaySales();
        return todaySales.reduce((total, sale) => total + sale.total, 0);
    }

    /**
     * حساب عدد طلبات اليوم
     */
    getTodayOrdersCount() {
        return this.getTodaySales().length;
    }

    /**
     * حفظ المبيعات
     */
    saveSales(sales) {
        return StorageUtils.save(this.storagePrefix + 'sales', sales);
    }

    // ==================== إدارة الأنشطة ====================

    /**
     * الحصول على جميع الأنشطة
     */
    getActivities() {
        return StorageUtils.load(this.storagePrefix + 'activities', []);
    }

    /**
     * إضافة نشاط جديد
     */
    addActivity(description, type = 'general', data = null) {
        const activities = this.getActivities();
        const newActivity = {
            id: this.generateId(),
            description,
            type,
            data,
            timestamp: new Date().toISOString()
        };
        
        activities.unshift(newActivity); // إضافة في المقدمة
        
        // الاحتفاظ بآخر 100 نشاط فقط
        if (activities.length > 100) {
            activities.splice(100);
        }
        
        this.saveActivities(activities);
        return newActivity;
    }

    /**
     * الحصول على الأنشطة الحديثة
     */
    getRecentActivities(limit = 10) {
        const activities = this.getActivities();
        return activities.slice(0, limit);
    }

    /**
     * حفظ الأنشطة
     */
    saveActivities(activities) {
        return StorageUtils.save(this.storagePrefix + 'activities', activities);
    }

    // ==================== إدارة الإعدادات ====================

    /**
     * الحصول على الإعدادات
     */
    getSettings() {
        return StorageUtils.load(this.storagePrefix + 'settings', this.getDefaultSettings());
    }

    /**
     * تحديث الإعدادات
     */
    updateSettings(newSettings) {
        const currentSettings = this.getSettings();
        const updatedSettings = { ...currentSettings, ...newSettings };
        StorageUtils.save(this.storagePrefix + 'settings', updatedSettings);
        this.addActivity('تم تحديث إعدادات النظام', 'settings');
        return updatedSettings;
    }

    // ==================== النسخ الاحتياطي والاستعادة ====================

    /**
     * إنشاء نسخة احتياطية
     */
    createBackup() {
        const backup = {
            version: '1.0',
            timestamp: new Date().toISOString(),
            data: {
                products: this.getProducts(),
                customers: this.getCustomers(),
                suppliers: this.getSuppliers(),
                sales: this.getSales(),
                settings: this.getSettings(),
                activities: this.getActivities()
            }
        };
        
        this.addActivity('تم إنشاء نسخة احتياطية', 'backup');
        return backup;
    }

    /**
     * استعادة من نسخة احتياطية
     */
    restoreFromBackup(backupData) {
        try {
            if (!backupData.data) {
                throw new Error('بيانات النسخة الاحتياطية غير صالحة');
            }

            // استعادة البيانات
            if (backupData.data.products) {
                this.saveProducts(backupData.data.products);
            }
            if (backupData.data.customers) {
                this.saveCustomers(backupData.data.customers);
            }
            if (backupData.data.suppliers) {
                this.saveSuppliers(backupData.data.suppliers);
            }
            if (backupData.data.sales) {
                this.saveSales(backupData.data.sales);
            }
            if (backupData.data.settings) {
                StorageUtils.save(this.storagePrefix + 'settings', backupData.data.settings);
            }
            if (backupData.data.activities) {
                this.saveActivities(backupData.data.activities);
            }

            this.addActivity('تم استعادة البيانات من النسخة الاحتياطية', 'restore');
            return true;
        } catch (error) {
            console.error('خطأ في استعادة النسخة الاحتياطية:', error);
            return false;
        }
    }

    // ==================== الإحصائيات ====================

    /**
     * الحصول على إحصائيات عامة
     */
    getStatistics() {
        const products = this.getProducts();
        const customers = this.getCustomers();
        const sales = this.getSales();
        const todaySales = this.getTodaySales();

        return {
            totalProducts: products.length,
            totalCustomers: customers.length,
            totalSales: sales.length,
            todayTotalSales: this.getTodayTotalSales(),
            todayOrdersCount: this.getTodayOrdersCount(),
            lowStockProducts: this.getLowStockProducts().length,
            topSellingProducts: this.getTopSellingProducts(5),
            recentActivities: this.getRecentActivities(5)
        };
    }

    /**
     * الحصول على أفضل المنتجات مبيعاً
     */
    getTopSellingProducts(limit = 10) {
        const sales = this.getSales();
        const productSales = {};

        // حساب مبيعات كل منتج
        sales.forEach(sale => {
            sale.items.forEach(item => {
                if (!productSales[item.productId]) {
                    productSales[item.productId] = {
                        productId: item.productId,
                        productName: item.productName,
                        totalQuantity: 0,
                        totalRevenue: 0
                    };
                }
                productSales[item.productId].totalQuantity += item.quantity;
                productSales[item.productId].totalRevenue += item.total;
            });
        });

        // ترتيب حسب الكمية المباعة
        return Object.values(productSales)
            .sort((a, b) => b.totalQuantity - a.totalQuantity)
            .slice(0, limit);
    }
}

// إنشاء مثيل واحد من قاعدة البيانات
const database = new Database();

// تصدير قاعدة البيانات للاستخدام في ملفات أخرى
window.database = database;
